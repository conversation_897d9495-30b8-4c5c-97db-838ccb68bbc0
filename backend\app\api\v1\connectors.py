"""
JuliusAI Data Connectors API Endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Optional, List
import logging
from uuid import UUID

from app.database import get_db
from app.schemas.connector import (
    DataSource,
    DataSourceCreate,
    DataSourceUpdate,
    DataSourceList,
    DataSyncJob,
    DataSyncJobCreate,
    DataSyncJobList,
    APICredential,
    APICredentialCreate,
    APICredentialUpdate,
    APICredentialList,
    DataConnectorTemplate,
    DataConnectorTemplateCreate,
    DataConnectorTemplateList,
    ConnectionTestRequest,
    ConnectionTestResult,
    DataPreview,
    DataPreviewRequest,
)
from app.services.connector_service import ConnectorService
from app.core.deps import get_current_active_user
from app.models.user import User
from app.models.connector import (
    DataSource as DataSourceDB,
    DataSyncJob as DataSyncJobDB,
    APICredential as APICredentialDB,
    DataConnectorTemplate as DataConnectorTemplateDB,
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/connectors", tags=["Data Connectors"])


# Data Source Endpoints
@router.post("/sources", response_model=DataSource, status_code=status.HTTP_201_CREATED)
async def create_data_source(
    source_data: DataSourceCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> DataSource:
    """
    Create a new data source.

    Args:
        source_data: Data source configuration
        current_user: Current authenticated user
        db: Database session

    Returns:
        DataSource: Created data source instance
    """
    try:
        connector_service = ConnectorService(db)
        data_source = await connector_service.create_data_source(
            source_data, current_user
        )

        logger.info(f"Data source created by user {current_user.id}: {data_source.id}")
        return data_source

    except Exception as e:
        logger.error(f"Error creating data source: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create data source",
        )


@router.get("/sources", response_model=DataSourceList)
async def list_data_sources(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    search: Optional[str] = Query(None, description="Search term"),
    provider: Optional[str] = Query(None, description="Filter by provider"),
    source_type: Optional[str] = Query(None, description="Filter by source type"),
    status: Optional[str] = Query(None, description="Filter by status"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> DataSourceList:
    """
    List data sources for the current user's organization.

    Args:
        page: Page number
        size: Page size
        search: Search term for source names
        provider: Filter by provider
        source_type: Filter by source type
        status: Filter by status
        current_user: Current authenticated user
        db: Database session

    Returns:
        DataSourceList: Paginated list of data sources
    """
    try:
        query = db.query(DataSourceDB).filter(
            DataSourceDB.organization_id == current_user.organization_id,
            DataSourceDB.is_active == True,
        )

        # Apply filters
        if search:
            query = query.filter(DataSourceDB.name.ilike(f"%{search}%"))
        if provider:
            query = query.filter(DataSourceDB.provider == provider)
        if source_type:
            query = query.filter(DataSourceDB.source_type == source_type)
        if status:
            query = query.filter(DataSourceDB.status == status)

        # Get total count
        total = query.count()

        # Apply pagination
        skip = (page - 1) * size
        sources = (
            query.order_by(DataSourceDB.created_at.desc())
            .offset(skip)
            .limit(size)
            .all()
        )

        pages = (total + size - 1) // size

        return DataSourceList(
            data_sources=sources, total=total, page=page, size=size, pages=pages
        )

    except Exception as e:
        logger.error(f"Error listing data sources: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve data sources",
        )


@router.get("/sources/{source_id}", response_model=DataSource)
async def get_data_source(
    source_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> DataSource:
    """
    Get a specific data source.

    Args:
        source_id: Data source ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        DataSource: Data source details
    """
    try:
        source = (
            db.query(DataSourceDB)
            .filter(
                DataSourceDB.id == source_id,
                DataSourceDB.organization_id == current_user.organization_id,
            )
            .first()
        )

        if not source:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Data source not found"
            )

        return source

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting data source: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve data source",
        )


@router.put("/sources/{source_id}", response_model=DataSource)
async def update_data_source(
    source_id: UUID,
    source_update: DataSourceUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> DataSource:
    """
    Update a data source.

    Args:
        source_id: Data source ID
        source_update: Data source update data
        current_user: Current authenticated user
        db: Database session

    Returns:
        DataSource: Updated data source
    """
    try:
        source = (
            db.query(DataSourceDB)
            .filter(
                DataSourceDB.id == source_id,
                DataSourceDB.organization_id == current_user.organization_id,
            )
            .first()
        )

        if not source:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Data source not found"
            )

        # Update source fields
        update_data = source_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            if field == "authentication_config" and value:
                # Re-encrypt authentication config
                connector_service = ConnectorService(db)
                value = connector_service._encrypt_credentials(value)
            setattr(source, field, value)

        db.commit()
        db.refresh(source)

        logger.info(f"Data source updated: {source.id}")
        return source

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating data source: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update data source",
        )


# Connection Testing Endpoints
@router.post("/test-connection", response_model=ConnectionTestResult)
async def test_connection(
    test_request: ConnectionTestRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> ConnectionTestResult:
    """
    Test connection to external data source.

    Args:
        test_request: Connection test configuration
        current_user: Current authenticated user
        db: Database session

    Returns:
        ConnectionTestResult: Test results
    """
    try:
        connector_service = ConnectorService(db)
        result = await connector_service.test_connection(test_request, current_user)

        logger.info(
            f"Connection test performed by user {current_user.id}: {result.success}"
        )
        return result

    except Exception as e:
        logger.error(f"Error testing connection: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to test connection",
        )


# Sync Job Endpoints
@router.post(
    "/sync-jobs", response_model=DataSyncJob, status_code=status.HTTP_201_CREATED
)
async def create_sync_job(
    job_data: DataSyncJobCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> DataSyncJob:
    """
    Create and start a data synchronization job.

    Args:
        job_data: Sync job configuration
        background_tasks: Background task manager
        current_user: Current authenticated user
        db: Database session

    Returns:
        DataSyncJob: Created sync job instance
    """
    try:
        connector_service = ConnectorService(db)
        sync_job = await connector_service.create_sync_job(job_data, current_user)

        logger.info(f"Sync job created by user {current_user.id}: {sync_job.id}")
        return sync_job

    except Exception as e:
        logger.error(f"Error creating sync job: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create sync job",
        )


@router.get("/sync-jobs", response_model=DataSyncJobList)
async def list_sync_jobs(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    data_source_id: Optional[UUID] = Query(
        None, description="Filter by data source ID"
    ),
    status: Optional[str] = Query(None, description="Filter by status"),
    job_type: Optional[str] = Query(None, description="Filter by job type"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> DataSyncJobList:
    """
    List sync jobs for the current user's organization.

    Args:
        page: Page number
        size: Page size
        data_source_id: Filter by data source ID
        status: Filter by job status
        job_type: Filter by job type
        current_user: Current authenticated user
        db: Database session

    Returns:
        DataSyncJobList: Paginated list of sync jobs
    """
    try:
        # Join with data sources to filter by organization
        query = (
            db.query(DataSyncJobDB)
            .join(DataSourceDB)
            .filter(DataSourceDB.organization_id == current_user.organization_id)
        )

        # Apply filters
        if data_source_id:
            query = query.filter(DataSyncJobDB.data_source_id == data_source_id)
        if status:
            query = query.filter(DataSyncJobDB.status == status)
        if job_type:
            query = query.filter(DataSyncJobDB.job_type == job_type)

        # Get total count
        total = query.count()

        # Apply pagination
        skip = (page - 1) * size
        jobs = (
            query.order_by(DataSyncJobDB.created_at.desc())
            .offset(skip)
            .limit(size)
            .all()
        )

        pages = (total + size - 1) // size

        return DataSyncJobList(
            sync_jobs=jobs, total=total, page=page, size=size, pages=pages
        )

    except Exception as e:
        logger.error(f"Error listing sync jobs: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve sync jobs",
        )


@router.get("/sync-jobs/{job_id}", response_model=DataSyncJob)
async def get_sync_job(
    job_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> DataSyncJob:
    """
    Get a specific sync job.

    Args:
        job_id: Sync job ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        DataSyncJob: Sync job details with progress
    """
    try:
        job = (
            db.query(DataSyncJobDB)
            .join(DataSourceDB)
            .filter(
                DataSyncJobDB.id == job_id,
                DataSourceDB.organization_id == current_user.organization_id,
            )
            .first()
        )

        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Sync job not found"
            )

        return job

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting sync job: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve sync job",
        )


# API Credential Endpoints
@router.post(
    "/credentials", response_model=APICredential, status_code=status.HTTP_201_CREATED
)
async def create_api_credential(
    credential_data: APICredentialCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> APICredential:
    """
    Create new API credentials.

    Args:
        credential_data: Credential configuration
        current_user: Current authenticated user
        db: Database session

    Returns:
        APICredential: Created credential instance
    """
    try:
        connector_service = ConnectorService(db)

        # Encrypt credentials
        encrypted_credentials = connector_service._encrypt_credentials(
            credential_data.credentials
        )

        db_credential = APICredentialDB(
            organization_id=current_user.organization_id,
            user_id=current_user.id,
            name=credential_data.name,
            description=credential_data.description,
            provider=credential_data.provider,
            credential_type=credential_data.credential_type,
            encrypted_credentials=encrypted_credentials,
            expires_at=credential_data.expires_at,
        )

        db.add(db_credential)
        db.commit()
        db.refresh(db_credential)

        logger.info(
            f"API credential created by user {current_user.id}: {db_credential.id}"
        )
        return db_credential

    except Exception as e:
        logger.error(f"Error creating API credential: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create API credential",
        )


@router.get("/credentials", response_model=APICredentialList)
async def list_api_credentials(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    provider: Optional[str] = Query(None, description="Filter by provider"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> APICredentialList:
    """
    List API credentials for the current user's organization.

    Args:
        page: Page number
        size: Page size
        provider: Filter by provider
        current_user: Current authenticated user
        db: Database session

    Returns:
        APICredentialList: Paginated list of credentials
    """
    try:
        query = db.query(APICredentialDB).filter(
            APICredentialDB.organization_id == current_user.organization_id,
            APICredentialDB.is_active == True,
        )

        # Apply filters
        if provider:
            query = query.filter(APICredentialDB.provider == provider)

        # Get total count
        total = query.count()

        # Apply pagination
        skip = (page - 1) * size
        credentials = (
            query.order_by(APICredentialDB.created_at.desc())
            .offset(skip)
            .limit(size)
            .all()
        )

        pages = (total + size - 1) // size

        return APICredentialList(
            credentials=credentials, total=total, page=page, size=size, pages=pages
        )

    except Exception as e:
        logger.error(f"Error listing API credentials: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve API credentials",
        )


# Template Endpoints
@router.get("/templates", response_model=DataConnectorTemplateList)
async def list_connector_templates(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    provider: Optional[str] = Query(None, description="Filter by provider"),
    category: Optional[str] = Query(None, description="Filter by category"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> DataConnectorTemplateList:
    """
    List available connector templates.

    Args:
        page: Page number
        size: Page size
        provider: Filter by provider
        category: Filter by category
        current_user: Current authenticated user
        db: Database session

    Returns:
        DataConnectorTemplateList: Paginated list of templates
    """
    try:
        query = db.query(DataConnectorTemplateDB).filter(
            DataConnectorTemplateDB.is_active == True
        )

        # Apply filters
        if provider:
            query = query.filter(DataConnectorTemplateDB.provider == provider)
        if category:
            query = query.filter(DataConnectorTemplateDB.category == category)

        # Get total count
        total = query.count()

        # Apply pagination
        skip = (page - 1) * size
        templates = (
            query.order_by(DataConnectorTemplateDB.usage_count.desc())
            .offset(skip)
            .limit(size)
            .all()
        )

        pages = (total + size - 1) // size

        return DataConnectorTemplateList(
            templates=templates, total=total, page=page, size=size, pages=pages
        )

    except Exception as e:
        logger.error(f"Error listing connector templates: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve connector templates",
        )


@router.get("/templates/{template_id}", response_model=DataConnectorTemplate)
async def get_connector_template(
    template_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> DataConnectorTemplate:
    """
    Get a specific connector template.

    Args:
        template_id: Template ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        DataConnectorTemplate: Template details
    """
    try:
        template = (
            db.query(DataConnectorTemplateDB)
            .filter(
                DataConnectorTemplateDB.id == template_id,
                DataConnectorTemplateDB.is_active == True,
            )
            .first()
        )

        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Connector template not found",
            )

        # Increment usage count
        template.usage_count += 1
        db.commit()

        return template

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting connector template: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve connector template",
        )


# Utility Endpoints
@router.get("/providers", response_model=List[str])
async def get_supported_providers() -> List[str]:
    """
    Get list of supported data providers.

    Returns:
        List[str]: Supported providers
    """
    return [
        "yahoo_finance",
        "alpha_vantage",
        "quandl",
        "bloomberg",
        "aws_s3",
        "google_cloud",
        "dropbox",
        "quickbooks",
        "xero",
        "sap",
        "mysql",
        "postgresql",
        "mongodb",
    ]


@router.get("/source-types", response_model=List[str])
async def get_source_types() -> List[str]:
    """
    Get list of supported source types.

    Returns:
        List[str]: Supported source types
    """
    return ["api", "database", "cloud_storage", "file_share"]


@router.get("/sync-frequencies", response_model=List[str])
async def get_sync_frequencies() -> List[str]:
    """
    Get list of supported sync frequencies.

    Returns:
        List[str]: Supported sync frequencies
    """
    return ["manual", "hourly", "daily", "weekly", "monthly", "custom"]


@router.delete("/sources/{source_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_data_source(
    source_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """
    Delete a data source (soft delete).

    Args:
        source_id: Data source ID
        current_user: Current authenticated user
        db: Database session
    """
    try:
        source = (
            db.query(DataSourceDB)
            .filter(
                DataSourceDB.id == source_id,
                DataSourceDB.organization_id == current_user.organization_id,
            )
            .first()
        )

        if not source:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Data source not found"
            )

        # Soft delete
        source.is_active = False
        db.commit()

        logger.info(f"Data source deleted: {source_id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting data source: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete data source",
        )


@router.post(
    "/sources/{source_id}/sync",
    response_model=DataSyncJob,
    status_code=status.HTTP_201_CREATED,
)
async def trigger_sync(
    source_id: UUID,
    job_type: str = Query("manual", regex="^(full_sync|incremental|test|manual)$"),
    background_tasks: BackgroundTasks = BackgroundTasks(),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> DataSyncJob:
    """
    Trigger a manual sync for a data source.

    Args:
        source_id: Data source ID
        job_type: Type of sync job
        background_tasks: Background task manager
        current_user: Current authenticated user
        db: Database session

    Returns:
        DataSyncJob: Created sync job
    """
    try:
        # Verify data source exists
        source = (
            db.query(DataSourceDB)
            .filter(
                DataSourceDB.id == source_id,
                DataSourceDB.organization_id == current_user.organization_id,
            )
            .first()
        )

        if not source:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Data source not found"
            )

        # Create sync job
        job_data = DataSyncJobCreate(data_source_id=source_id, job_type=job_type)

        connector_service = ConnectorService(db)
        sync_job = await connector_service.create_sync_job(job_data, current_user)

        logger.info(f"Manual sync triggered for source {source_id}")
        return sync_job

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error triggering sync: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to trigger sync",
        )
