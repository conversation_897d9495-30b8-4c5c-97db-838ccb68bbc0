"""
JuliusAI Authentication Service
"""
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from datetime import datetime, timedelta
from typing import Optional, <PERSON>ple
import logging
import uuid

from app.models.user import User, Organization, UserSession
from app.schemas.user import User<PERSON><PERSON>, User<PERSON>ogin, User<PERSON><PERSON><PERSON>, Token, TokenData
from app.core.security import (
    hash_password, verify_password, create_access_token, 
    create_refresh_token, verify_token, hash_token
)
from app.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class AuthService:
    """Authentication service for user management and token handling."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def register_user(self, user_data: UserRegister, ip_address: str = None, user_agent: str = None) -> Token:
        """
        Register a new user and organization.
        
        Args:
            user_data: User registration data
            ip_address: Client IP address
            user_agent: Client user agent
            
        Returns:
            Token: Authentication tokens
            
        Raises:
            HTTPException: If registration fails
        """
        try:
            # Check if email already exists
            existing_user = self.db.query(User).filter(User.email == user_data.email).first()
            if existing_user:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already registered"
                )
            
            # Check if organization slug already exists
            existing_org = self.db.query(Organization).filter(
                Organization.slug == user_data.organization_slug
            ).first()
            if existing_org:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Organization slug already taken"
                )
            
            # Create organization
            organization = Organization(
                name=user_data.organization_name,
                slug=user_data.organization_slug,
                subscription_tier="basic"
            )
            self.db.add(organization)
            self.db.flush()  # Get organization ID
            
            # Create user
            hashed_password = hash_password(user_data.password)
            user = User(
                organization_id=organization.id,
                email=user_data.email,
                password_hash=hashed_password,
                first_name=user_data.first_name,
                last_name=user_data.last_name,
                role="admin",  # First user in organization is admin
                email_verified_at=datetime.utcnow()  # Auto-verify for MVP
            )
            self.db.add(user)
            self.db.flush()  # Get user ID
            
            # Create authentication tokens
            token_response = self._create_user_session(
                user, ip_address, user_agent, remember_me=False
            )
            
            self.db.commit()
            logger.info(f"User registered successfully: {user.email}")
            
            return token_response
            
        except HTTPException:
            self.db.rollback()
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"Registration error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Registration failed"
            )
    
    def authenticate_user(self, login_data: UserLogin, ip_address: str = None, user_agent: str = None) -> Token:
        """
        Authenticate a user and create session.
        
        Args:
            login_data: User login credentials
            ip_address: Client IP address
            user_agent: Client user agent
            
        Returns:
            Token: Authentication tokens
            
        Raises:
            HTTPException: If authentication fails
        """
        try:
            # Find user by email
            user = self.db.query(User).filter(
                User.email == login_data.email,
                User.is_active == True
            ).first()
            
            if not user or not verify_password(login_data.password, user.password_hash):
                logger.warning(f"Failed login attempt for email: {login_data.email}")
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Incorrect email or password"
                )
            
            # Check if organization is active
            if not user.organization.is_active:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Organization is inactive"
                )
            
            # Update last login time
            user.last_login_at = datetime.utcnow()
            
            # Create authentication tokens
            token_response = self._create_user_session(
                user, ip_address, user_agent, login_data.remember_me
            )
            
            self.db.commit()
            logger.info(f"User authenticated successfully: {user.email}")
            
            return token_response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Authentication failed"
            )
    
    def refresh_token(self, refresh_token: str, ip_address: str = None, user_agent: str = None) -> Token:
        """
        Refresh access token using refresh token.
        
        Args:
            refresh_token: Refresh token
            ip_address: Client IP address
            user_agent: Client user agent
            
        Returns:
            Token: New authentication tokens
            
        Raises:
            HTTPException: If refresh fails
        """
        try:
            # Verify refresh token
            token_data = verify_token(refresh_token, token_type="refresh")
            
            # Find user session
            session = self.db.query(UserSession).filter(
                UserSession.id == token_data.session_id,
                UserSession.user_id == token_data.user_id,
                UserSession.is_active == True
            ).first()
            
            if not session or session.is_expired:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid or expired refresh token"
                )
            
            # Get user
            user = self.db.query(User).filter(
                User.id == token_data.user_id,
                User.is_active == True
            ).first()
            
            if not user:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="User not found or inactive"
                )
            
            # Create new tokens
            token_response = self._create_user_session(
                user, ip_address, user_agent, remember_me=True, existing_session=session
            )
            
            self.db.commit()
            logger.info(f"Token refreshed for user: {user.email}")
            
            return token_response
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Token refresh error: {e}")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token refresh failed"
            )
    
    def logout_user(self, token_data: TokenData) -> bool:
        """
        Logout user by deactivating session.
        
        Args:
            token_data: Current user token data
            
        Returns:
            bool: True if logout successful
        """
        try:
            session = self.db.query(UserSession).filter(
                UserSession.id == token_data.session_id,
                UserSession.user_id == token_data.user_id
            ).first()
            
            if session:
                session.is_active = False
                self.db.commit()
                logger.info(f"User logged out: {token_data.email}")
            
            return True
            
        except Exception as e:
            logger.error(f"Logout error: {e}")
            return False
    
    def _create_user_session(
        self, 
        user: User, 
        ip_address: str = None, 
        user_agent: str = None, 
        remember_me: bool = False,
        existing_session: UserSession = None
    ) -> Token:
        """
        Create user session and authentication tokens.
        
        Args:
            user: User object
            ip_address: Client IP address
            user_agent: Client user agent
            remember_me: Whether to extend token expiration
            existing_session: Existing session to update
            
        Returns:
            Token: Authentication tokens
        """
        # Create session ID
        session_id = existing_session.id if existing_session else uuid.uuid4()
        
        # Token payload
        token_payload = {
            "user_id": str(user.id),
            "email": user.email,
            "organization_id": str(user.organization_id),
            "role": user.role,
            "session_id": str(session_id)
        }
        
        # Create tokens
        access_token_expires = timedelta(minutes=settings.access_token_expire_minutes)
        refresh_token_expires = timedelta(
            days=settings.refresh_token_expire_days if remember_me else 1
        )
        
        access_token = create_access_token(token_payload, access_token_expires)
        refresh_token = create_refresh_token(token_payload, refresh_token_expires)
        
        # Create or update session
        if existing_session:
            session = existing_session
            session.token_hash = hash_token(access_token)
            session.refresh_token_hash = hash_token(refresh_token)
            session.expires_at = datetime.utcnow() + refresh_token_expires
            session.last_used_at = datetime.utcnow()
            session.ip_address = ip_address
            session.user_agent = user_agent
        else:
            session = UserSession(
                id=session_id,
                user_id=user.id,
                token_hash=hash_token(access_token),
                refresh_token_hash=hash_token(refresh_token),
                ip_address=ip_address,
                user_agent=user_agent,
                expires_at=datetime.utcnow() + refresh_token_expires
            )
            self.db.add(session)
        
        # Create token response
        from app.schemas.user import User as UserSchema
        user_schema = UserSchema.from_orm(user)
        
        return Token(
            access_token=access_token,
            refresh_token=refresh_token,
            token_type="bearer",
            expires_in=int(access_token_expires.total_seconds()),
            user=user_schema
        )
