"""
JuliusAI Authentication Tests
"""
import pytest
from fastapi.testclient import Test<PERSON><PERSON>
from sqlalchemy.orm import Session

from app.models.user import User, Organization
from app.core.security import verify_password


class TestAuthEndpoints:
    """Test authentication API endpoints."""
    
    def test_register_user_success(self, client: TestClient, test_user_data: dict):
        """Test successful user registration."""
        response = client.post("/api/v1/auth/register", json=test_user_data)
        
        assert response.status_code == 201
        data = response.json()
        
        # Check response structure
        assert "access_token" in data
        assert "refresh_token" in data
        assert "token_type" in data
        assert "expires_in" in data
        assert "user" in data
        
        # Check user data
        user_data = data["user"]
        assert user_data["email"] == test_user_data["email"]
        assert user_data["first_name"] == test_user_data["first_name"]
        assert user_data["last_name"] == test_user_data["last_name"]
        assert user_data["role"] == "admin"  # First user is admin
        assert user_data["is_active"] is True
    
    def test_register_user_duplicate_email(self, client: TestClient, test_user_data: dict):
        """Test registration with duplicate email."""
        # Register first user
        client.post("/api/v1/auth/register", json=test_user_data)
        
        # Try to register with same email
        response = client.post("/api/v1/auth/register", json=test_user_data)
        
        assert response.status_code == 400
        data = response.json()
        assert "Email already registered" in data["message"]
    
    def test_register_user_duplicate_org_slug(self, client: TestClient, test_user_data: dict):
        """Test registration with duplicate organization slug."""
        # Register first user
        client.post("/api/v1/auth/register", json=test_user_data)
        
        # Try to register with same org slug but different email
        duplicate_data = test_user_data.copy()
        duplicate_data["email"] = "<EMAIL>"
        
        response = client.post("/api/v1/auth/register", json=duplicate_data)
        
        assert response.status_code == 400
        data = response.json()
        assert "Organization slug already taken" in data["message"]
    
    def test_register_user_invalid_password(self, client: TestClient, test_user_data: dict):
        """Test registration with invalid password."""
        invalid_data = test_user_data.copy()
        invalid_data["password"] = "weak"
        
        response = client.post("/api/v1/auth/register", json=invalid_data)
        
        assert response.status_code == 422
        data = response.json()
        assert "validation_errors" in data
    
    def test_login_success(self, client: TestClient, test_user_data: dict, test_login_data: dict):
        """Test successful user login."""
        # Register user first
        client.post("/api/v1/auth/register", json=test_user_data)
        
        # Login
        response = client.post("/api/v1/auth/login", json=test_login_data)
        
        assert response.status_code == 200
        data = response.json()
        
        # Check response structure
        assert "access_token" in data
        assert "refresh_token" in data
        assert "token_type" in data
        assert "expires_in" in data
        assert "user" in data
        
        # Check user data
        user_data = data["user"]
        assert user_data["email"] == test_login_data["email"]
    
    def test_login_invalid_credentials(self, client: TestClient, test_user_data: dict):
        """Test login with invalid credentials."""
        # Register user first
        client.post("/api/v1/auth/register", json=test_user_data)
        
        # Try login with wrong password
        invalid_login = {
            "email": test_user_data["email"],
            "password": "wrongpassword",
            "remember_me": False
        }
        
        response = client.post("/api/v1/auth/login", json=invalid_login)
        
        assert response.status_code == 401
        data = response.json()
        assert "Incorrect email or password" in data["message"]
    
    def test_login_nonexistent_user(self, client: TestClient):
        """Test login with nonexistent user."""
        login_data = {
            "email": "<EMAIL>",
            "password": "TestPassword123!",
            "remember_me": False
        }
        
        response = client.post("/api/v1/auth/login", json=login_data)
        
        assert response.status_code == 401
        data = response.json()
        assert "Incorrect email or password" in data["message"]
    
    def test_get_current_user(self, client: TestClient, test_user_data: dict, test_login_data: dict):
        """Test getting current user information."""
        # Register and login
        client.post("/api/v1/auth/register", json=test_user_data)
        login_response = client.post("/api/v1/auth/login", json=test_login_data)
        token = login_response.json()["access_token"]
        
        # Get current user
        headers = {"Authorization": f"Bearer {token}"}
        response = client.get("/api/v1/auth/me", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["email"] == test_user_data["email"]
        assert data["first_name"] == test_user_data["first_name"]
        assert data["last_name"] == test_user_data["last_name"]
    
    def test_get_current_user_invalid_token(self, client: TestClient):
        """Test getting current user with invalid token."""
        headers = {"Authorization": "Bearer invalid_token"}
        response = client.get("/api/v1/auth/me", headers=headers)
        
        assert response.status_code == 401
    
    def test_verify_token(self, client: TestClient, test_user_data: dict, test_login_data: dict):
        """Test token verification."""
        # Register and login
        client.post("/api/v1/auth/register", json=test_user_data)
        login_response = client.post("/api/v1/auth/login", json=test_login_data)
        token = login_response.json()["access_token"]
        
        # Verify token
        headers = {"Authorization": f"Bearer {token}"}
        response = client.get("/api/v1/auth/verify", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["valid"] is True
        assert data["email"] == test_user_data["email"]
        assert "user_id" in data
        assert "organization_id" in data
    
    def test_logout(self, client: TestClient, test_user_data: dict, test_login_data: dict):
        """Test user logout."""
        # Register and login
        client.post("/api/v1/auth/register", json=test_user_data)
        login_response = client.post("/api/v1/auth/login", json=test_login_data)
        token = login_response.json()["access_token"]
        
        # Logout
        headers = {"Authorization": f"Bearer {token}"}
        response = client.post("/api/v1/auth/logout", headers=headers)
        
        assert response.status_code == 200
        data = response.json()
        assert "Logged out successfully" in data["message"]
        
        # Try to use token after logout (should fail)
        response = client.get("/api/v1/auth/me", headers=headers)
        assert response.status_code == 401


class TestPasswordSecurity:
    """Test password security functions."""
    
    def test_password_hashing(self):
        """Test password hashing and verification."""
        from app.core.security import hash_password, verify_password
        
        password = "TestPassword123!"
        hashed = hash_password(password)
        
        # Password should be hashed
        assert hashed != password
        assert len(hashed) > 50  # bcrypt hashes are long
        
        # Verification should work
        assert verify_password(password, hashed) is True
        assert verify_password("wrongpassword", hashed) is False
    
    def test_password_strength_check(self):
        """Test password strength validation."""
        from app.core.security import check_password_strength
        
        # Strong password
        strong_result = check_password_strength("StrongPassword123!")
        assert strong_result["is_strong"] is True
        assert strong_result["score"] == 5
        assert len(strong_result["issues"]) == 0
        
        # Weak password
        weak_result = check_password_strength("weak")
        assert weak_result["is_strong"] is False
        assert weak_result["score"] < 5
        assert len(weak_result["issues"]) > 0
