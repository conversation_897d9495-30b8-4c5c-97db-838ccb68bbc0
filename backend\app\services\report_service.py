"""
JuliusAI Report Generation Service
"""

import pandas as pd
import numpy as np
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON>Ex<PERSON>, status
from typing import Optional, List, Dict, Any, Tuple
import logging
from datetime import datetime, timedelta
import json
import os
from pathlib import Path
import asyncio
from jinja2 import Template, Environment, FileSystemLoader
import matplotlib.pyplot as plt
import seaborn as sns
import plotly.graph_objects as go
import plotly.express as px
from io import BytesIO
import base64

from app.models.report import (
    Analysis,
    AnalysisMetric,
    Report,
    ReportTemplate,
    ReportSection,
    InsightTemplate,
    ReportExport,
)
from app.models.data import Dataset
from app.models.user import User
from app.models.forecast import Forecast, ForecastModel
from app.schemas.report import (
    AnalysisCreate,
    ReportCreate,
    ReportGenerate,
    ReportTemplateCreate,
    GeneratedInsight,
    DataSummary,
)
from app.services.data_service import DataService
from app.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class ReportService:
    """Service for handling report generation and analysis."""

    def __init__(self, db: Session):
        self.db = db
        self.data_service = DataService(db)
        self.reports_dir = Path(settings.upload_dir) / "reports"
        self.templates_dir = Path(settings.upload_dir) / "templates"
        self.reports_dir.mkdir(parents=True, exist_ok=True)
        self.templates_dir.mkdir(parents=True, exist_ok=True)

        # Initialize Jinja2 environment
        self.jinja_env = Environment(
            loader=FileSystemLoader(str(self.templates_dir)), autoescape=True
        )

    async def create_analysis(
        self, analysis_data: AnalysisCreate, current_user: User
    ) -> Analysis:
        """
        Create a new analysis.

        Args:
            analysis_data: Analysis configuration data
            current_user: Current authenticated user

        Returns:
            Analysis: Created analysis instance
        """
        try:
            # Verify dataset exists and user has access
            dataset = (
                self.db.query(Dataset)
                .filter(
                    Dataset.id == analysis_data.dataset_id,
                    Dataset.organization_id == current_user.organization_id,
                )
                .first()
            )

            if not dataset:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Dataset not found"
                )

            # Create analysis instance
            db_analysis = Analysis(
                organization_id=current_user.organization_id,
                dataset_id=analysis_data.dataset_id,
                user_id=current_user.id,
                name=analysis_data.name,
                description=analysis_data.description,
                analysis_type=analysis_data.analysis_type,
                parameters=analysis_data.parameters or {},
                tags=analysis_data.tags or [],
                status="pending",
            )

            self.db.add(db_analysis)
            self.db.commit()
            self.db.refresh(db_analysis)

            # Start analysis processing in background
            await self._process_analysis(db_analysis, dataset)

            logger.info(f"Analysis created: {db_analysis.id}")
            return db_analysis

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating analysis: {e}")
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create analysis",
            )

    async def generate_report(
        self, report_data: ReportGenerate, current_user: User
    ) -> Report:
        """
        Generate a comprehensive report with AI-powered content.

        Args:
            report_data: Report generation configuration
            current_user: Current authenticated user

        Returns:
            Report: Generated report instance
        """
        try:
            # Validate analysis if provided
            analysis = None
            if report_data.analysis_id:
                analysis = (
                    self.db.query(Analysis)
                    .filter(
                        Analysis.id == report_data.analysis_id,
                        Analysis.organization_id == current_user.organization_id,
                    )
                    .first()
                )

                if not analysis:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Analysis not found",
                    )

            # Validate template if provided
            template = None
            if report_data.template_id:
                template = (
                    self.db.query(ReportTemplate)
                    .filter(
                        ReportTemplate.id == report_data.template_id,
                        ReportTemplate.organization_id == current_user.organization_id,
                    )
                    .first()
                )

                if not template:
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail="Report template not found",
                    )

            # Create report instance
            db_report = Report(
                organization_id=current_user.organization_id,
                analysis_id=report_data.analysis_id,
                template_id=report_data.template_id,
                user_id=current_user.id,
                title=report_data.title,
                report_type=report_data.report_type,
                audience=report_data.audience,
                generation_parameters=report_data.generation_parameters or {},
                status="generating",
            )

            self.db.add(db_report)
            self.db.commit()
            self.db.refresh(db_report)

            # Generate report content
            await self._generate_report_content(
                db_report, analysis, template, report_data, current_user
            )

            logger.info(f"Report generated: {db_report.id}")
            return db_report

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error generating report: {e}")
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to generate report",
            )

    async def _process_analysis(self, analysis: Analysis, dataset: Dataset):
        """Process analysis and generate insights."""
        try:
            analysis.status = "running"
            analysis.started_at = datetime.utcnow()
            self.db.commit()

            # Load dataset
            df = await self._load_dataset(dataset)

            # Perform analysis based on type
            if analysis.analysis_type == "descriptive":
                results = await self._perform_descriptive_analysis(df, analysis)
            elif analysis.analysis_type == "trend":
                results = await self._perform_trend_analysis(df, analysis)
            elif analysis.analysis_type == "correlation":
                results = await self._perform_correlation_analysis(df, analysis)
            elif analysis.analysis_type == "anomaly":
                results = await self._perform_anomaly_analysis(df, analysis)
            else:
                results = await self._perform_custom_analysis(df, analysis)

            # Generate AI insights
            insights = await self._generate_ai_insights(results, analysis, df)

            # Update analysis with results
            analysis.results = results
            analysis.insights = insights
            analysis.status = "completed"
            analysis.completed_at = datetime.utcnow()
            analysis.execution_time_ms = int(
                (analysis.completed_at - analysis.started_at).total_seconds() * 1000
            )

            # Store metrics
            await self._store_analysis_metrics(analysis, results)

            self.db.commit()

        except Exception as e:
            logger.error(f"Error processing analysis: {e}")
            analysis.status = "failed"
            analysis.error_message = str(e)
            self.db.commit()

    async def _load_dataset(self, dataset: Dataset) -> pd.DataFrame:
        """Load dataset from file."""
        try:
            if dataset.file_type == "csv":
                df = pd.read_csv(dataset.file_path)
            elif dataset.file_type in ["xlsx", "xls"]:
                df = pd.read_excel(dataset.file_path)
            else:
                raise ValueError(f"Unsupported file type: {dataset.file_type}")

            return df

        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
            raise

    async def _perform_descriptive_analysis(
        self, df: pd.DataFrame, analysis: Analysis
    ) -> Dict[str, Any]:
        """Perform descriptive statistical analysis."""
        try:
            numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()
            categorical_columns = df.select_dtypes(include=["object"]).columns.tolist()

            results = {
                "summary_statistics": {},
                "data_overview": {
                    "total_rows": len(df),
                    "total_columns": len(df.columns),
                    "numeric_columns": len(numeric_columns),
                    "categorical_columns": len(categorical_columns),
                    "missing_values": df.isnull().sum().to_dict(),
                    "data_types": df.dtypes.astype(str).to_dict(),
                },
                "distributions": {},
                "correlations": {},
            }

            # Summary statistics for numeric columns
            if numeric_columns:
                desc_stats = df[numeric_columns].describe()
                results["summary_statistics"] = desc_stats.to_dict()

                # Correlation matrix
                if len(numeric_columns) > 1:
                    corr_matrix = df[numeric_columns].corr()
                    results["correlations"] = corr_matrix.to_dict()

            # Value counts for categorical columns
            for col in categorical_columns[
                :10
            ]:  # Limit to first 10 categorical columns
                value_counts = df[col].value_counts().head(20)
                results["distributions"][col] = value_counts.to_dict()

            return results

        except Exception as e:
            logger.error(f"Error in descriptive analysis: {e}")
            raise

    async def _perform_trend_analysis(
        self, df: pd.DataFrame, analysis: Analysis
    ) -> Dict[str, Any]:
        """Perform trend analysis on time series data."""
        try:
            # Try to identify date columns
            date_columns = df.select_dtypes(include=["datetime64"]).columns.tolist()
            if not date_columns:
                # Try to convert potential date columns
                for col in df.columns:
                    try:
                        df[col] = pd.to_datetime(df[col])
                        date_columns.append(col)
                        break
                    except:
                        continue

            if not date_columns:
                raise ValueError("No date column found for trend analysis")

            date_col = date_columns[0]
            numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()

            results = {
                "trends": {},
                "seasonal_patterns": {},
                "growth_rates": {},
                "trend_statistics": {},
            }

            # Sort by date
            df_sorted = df.sort_values(date_col)

            for col in numeric_columns[:5]:  # Analyze first 5 numeric columns
                # Calculate trend
                values = df_sorted[col].dropna()
                if len(values) > 1:
                    # Simple linear trend
                    x = np.arange(len(values))
                    coeffs = np.polyfit(x, values, 1)
                    trend_slope = coeffs[0]

                    # Growth rate
                    if len(values) > 1:
                        growth_rate = (
                            (values.iloc[-1] - values.iloc[0]) / values.iloc[0]
                        ) * 100
                    else:
                        growth_rate = 0

                    results["trends"][col] = {
                        "slope": float(trend_slope),
                        "direction": "increasing" if trend_slope > 0 else "decreasing",
                        "strength": abs(float(trend_slope)),
                    }

                    results["growth_rates"][col] = float(growth_rate)

            return results

        except Exception as e:
            logger.error(f"Error in trend analysis: {e}")
            raise

    async def _perform_correlation_analysis(
        self, df: pd.DataFrame, analysis: Analysis
    ) -> Dict[str, Any]:
        """Perform correlation analysis."""
        try:
            numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()

            if len(numeric_columns) < 2:
                raise ValueError(
                    "Need at least 2 numeric columns for correlation analysis"
                )

            # Calculate correlation matrix
            corr_matrix = df[numeric_columns].corr()

            # Find strong correlations
            strong_correlations = []
            for i in range(len(corr_matrix.columns)):
                for j in range(i + 1, len(corr_matrix.columns)):
                    corr_value = corr_matrix.iloc[i, j]
                    if abs(corr_value) > 0.5:  # Strong correlation threshold
                        strong_correlations.append(
                            {
                                "variable1": corr_matrix.columns[i],
                                "variable2": corr_matrix.columns[j],
                                "correlation": float(corr_value),
                                "strength": "strong"
                                if abs(corr_value) > 0.7
                                else "moderate",
                            }
                        )

            results = {
                "correlation_matrix": corr_matrix.to_dict(),
                "strong_correlations": strong_correlations,
                "summary": {
                    "total_pairs": len(strong_correlations),
                    "strongest_correlation": max(
                        strong_correlations, key=lambda x: abs(x["correlation"])
                    )["correlation"]
                    if strong_correlations
                    else 0,
                },
            }

            return results

        except Exception as e:
            logger.error(f"Error in correlation analysis: {e}")
            raise

    async def _perform_anomaly_analysis(
        self, df: pd.DataFrame, analysis: Analysis
    ) -> Dict[str, Any]:
        """Perform anomaly detection analysis."""
        try:
            from sklearn.ensemble import IsolationForest
            from sklearn.preprocessing import StandardScaler

            numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()

            if not numeric_columns:
                raise ValueError("No numeric columns found for anomaly detection")

            results = {"anomalies": {}, "anomaly_summary": {}, "outlier_statistics": {}}

            for col in numeric_columns[:5]:  # Analyze first 5 numeric columns
                values = df[col].dropna()

                if len(values) < 10:  # Need sufficient data
                    continue

                # Statistical outliers (IQR method)
                Q1 = values.quantile(0.25)
                Q3 = values.quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR

                statistical_outliers = values[
                    (values < lower_bound) | (values > upper_bound)
                ]

                # Machine learning based anomaly detection
                if len(values) > 20:
                    scaler = StandardScaler()
                    values_scaled = scaler.fit_transform(values.values.reshape(-1, 1))

                    iso_forest = IsolationForest(contamination=0.1, random_state=42)
                    anomaly_labels = iso_forest.fit_predict(values_scaled)

                    ml_anomalies = values[anomaly_labels == -1]
                else:
                    ml_anomalies = pd.Series([], dtype=float)

                results["anomalies"][col] = {
                    "statistical_outliers": statistical_outliers.tolist(),
                    "ml_anomalies": ml_anomalies.tolist(),
                    "outlier_count": len(statistical_outliers),
                    "anomaly_percentage": (len(statistical_outliers) / len(values))
                    * 100,
                }

                results["outlier_statistics"][col] = {
                    "lower_bound": float(lower_bound),
                    "upper_bound": float(upper_bound),
                    "median": float(values.median()),
                    "std": float(values.std()),
                }

            return results

        except Exception as e:
            logger.error(f"Error in anomaly analysis: {e}")
            raise

    async def _perform_custom_analysis(
        self, df: pd.DataFrame, analysis: Analysis
    ) -> Dict[str, Any]:
        """Perform custom analysis based on parameters."""
        try:
            parameters = analysis.parameters or {}

            results = {"custom_metrics": {}, "calculated_fields": {}, "summary": {}}

            # Basic custom analysis - can be extended based on parameters
            numeric_columns = df.select_dtypes(include=[np.number]).columns.tolist()

            if (
                "target_column" in parameters
                and parameters["target_column"] in numeric_columns
            ):
                target_col = parameters["target_column"]
                target_values = df[target_col].dropna()

                results["custom_metrics"][target_col] = {
                    "mean": float(target_values.mean()),
                    "median": float(target_values.median()),
                    "std": float(target_values.std()),
                    "min": float(target_values.min()),
                    "max": float(target_values.max()),
                    "range": float(target_values.max() - target_values.min()),
                }

            return results

        except Exception as e:
            logger.error(f"Error in custom analysis: {e}")
            raise

    async def _generate_ai_insights(
        self, results: Dict[str, Any], analysis: Analysis, df: pd.DataFrame
    ) -> Dict[str, Any]:
        """Generate AI-powered insights from analysis results."""
        try:
            insights = {
                "key_findings": [],
                "recommendations": [],
                "data_quality_insights": [],
                "business_implications": [],
            }

            # Generate insights based on analysis type
            if analysis.analysis_type == "descriptive":
                insights["key_findings"] = await self._generate_descriptive_insights(
                    results, df
                )
            elif analysis.analysis_type == "trend":
                insights["key_findings"] = await self._generate_trend_insights(
                    results, df
                )
            elif analysis.analysis_type == "correlation":
                insights["key_findings"] = await self._generate_correlation_insights(
                    results, df
                )
            elif analysis.analysis_type == "anomaly":
                insights["key_findings"] = await self._generate_anomaly_insights(
                    results, df
                )

            # Generate data quality insights
            insights[
                "data_quality_insights"
            ] = await self._generate_data_quality_insights(df)

            # Generate recommendations
            insights["recommendations"] = await self._generate_recommendations(
                results, analysis.analysis_type
            )

            return insights

        except Exception as e:
            logger.error(f"Error generating AI insights: {e}")
            return {
                "key_findings": [],
                "recommendations": [],
                "data_quality_insights": [],
            }

    async def _generate_descriptive_insights(
        self, results: Dict[str, Any], df: pd.DataFrame
    ) -> List[str]:
        """Generate insights for descriptive analysis."""
        insights = []

        try:
            data_overview = results.get("data_overview", {})
            summary_stats = results.get("summary_statistics", {})

            # Data size insights
            total_rows = data_overview.get("total_rows", 0)
            total_cols = data_overview.get("total_columns", 0)
            insights.append(
                f"Dataset contains {total_rows:,} records across {total_cols} variables"
            )

            # Missing data insights
            missing_values = data_overview.get("missing_values", {})
            total_missing = sum(missing_values.values())
            if total_missing > 0:
                missing_pct = (total_missing / (total_rows * total_cols)) * 100
                insights.append(
                    f"Data completeness: {missing_pct:.1f}% of values are missing"
                )

            # Statistical insights
            for col, stats in summary_stats.items():
                if isinstance(stats, dict) and "mean" in stats:
                    mean_val = stats["mean"]
                    std_val = stats.get("std", 0)
                    if std_val > 0:
                        cv = (std_val / mean_val) * 100
                        if cv > 50:
                            insights.append(
                                f"{col} shows high variability (CV: {cv:.1f}%)"
                            )

        except Exception as e:
            logger.error(f"Error generating descriptive insights: {e}")

        return insights

    async def _generate_trend_insights(
        self, results: Dict[str, Any], df: pd.DataFrame
    ) -> List[str]:
        """Generate insights for trend analysis."""
        insights = []

        try:
            trends = results.get("trends", {})
            growth_rates = results.get("growth_rates", {})

            for col, trend_data in trends.items():
                direction = trend_data.get("direction", "stable")
                slope = trend_data.get("slope", 0)
                growth_rate = growth_rates.get(col, 0)

                if abs(growth_rate) > 10:
                    insights.append(
                        f"{col} shows significant {direction} trend with {growth_rate:.1f}% growth"
                    )
                elif abs(slope) > 0.1:
                    insights.append(
                        f"{col} demonstrates a {direction} pattern over time"
                    )

        except Exception as e:
            logger.error(f"Error generating trend insights: {e}")

        return insights

    async def _generate_correlation_insights(
        self, results: Dict[str, Any], df: pd.DataFrame
    ) -> List[str]:
        """Generate insights for correlation analysis."""
        insights = []

        try:
            strong_correlations = results.get("strong_correlations", [])

            for corr in strong_correlations[:5]:  # Top 5 correlations
                var1 = corr["variable1"]
                var2 = corr["variable2"]
                corr_value = corr["correlation"]
                strength = corr["strength"]

                direction = "positive" if corr_value > 0 else "negative"
                insights.append(
                    f"{strength.title()} {direction} correlation between {var1} and {var2} (r={corr_value:.3f})"
                )

        except Exception as e:
            logger.error(f"Error generating correlation insights: {e}")

        return insights

    async def _generate_anomaly_insights(
        self, results: Dict[str, Any], df: pd.DataFrame
    ) -> List[str]:
        """Generate insights for anomaly analysis."""
        insights = []

        try:
            anomalies = results.get("anomalies", {})

            for col, anomaly_data in anomalies.items():
                outlier_count = anomaly_data.get("outlier_count", 0)
                anomaly_pct = anomaly_data.get("anomaly_percentage", 0)

                if anomaly_pct > 5:
                    insights.append(
                        f"{col} contains {outlier_count} outliers ({anomaly_pct:.1f}% of data)"
                    )
                elif outlier_count > 0:
                    insights.append(
                        f"{col} has {outlier_count} potential anomalies requiring investigation"
                    )

        except Exception as e:
            logger.error(f"Error generating anomaly insights: {e}")

        return insights

    async def _generate_data_quality_insights(self, df: pd.DataFrame) -> List[str]:
        """Generate data quality insights."""
        insights = []

        try:
            # Check for duplicates
            duplicate_count = df.duplicated().sum()
            if duplicate_count > 0:
                insights.append(
                    f"Found {duplicate_count} duplicate records that may need attention"
                )

            # Check data types
            object_cols = df.select_dtypes(include=["object"]).columns
            for col in object_cols:
                unique_ratio = df[col].nunique() / len(df)
                if unique_ratio > 0.9:
                    insights.append(
                        f"{col} appears to contain mostly unique values (potential ID field)"
                    )

        except Exception as e:
            logger.error(f"Error generating data quality insights: {e}")

        return insights

    async def _generate_recommendations(
        self, results: Dict[str, Any], analysis_type: str
    ) -> List[str]:
        """Generate actionable recommendations."""
        recommendations = []

        try:
            if analysis_type == "descriptive":
                recommendations.append(
                    "Consider performing trend analysis to understand temporal patterns"
                )
                recommendations.append("Investigate correlations between key variables")
            elif analysis_type == "trend":
                recommendations.append(
                    "Use forecasting models to predict future values"
                )
                recommendations.append(
                    "Monitor trend changes for early warning signals"
                )
            elif analysis_type == "correlation":
                recommendations.append(
                    "Investigate causal relationships for strong correlations"
                )
                recommendations.append(
                    "Consider multivariate analysis for complex relationships"
                )
            elif analysis_type == "anomaly":
                recommendations.append(
                    "Investigate root causes of identified anomalies"
                )
                recommendations.append(
                    "Implement monitoring for similar anomalies in future data"
                )

        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")

        return recommendations

    async def _store_analysis_metrics(
        self, analysis: Analysis, results: Dict[str, Any]
    ):
        """Store analysis metrics in the database."""
        try:
            # Extract and store key metrics
            if analysis.analysis_type == "descriptive":
                summary_stats = results.get("summary_statistics", {})
                for col, stats in summary_stats.items():
                    if isinstance(stats, dict):
                        for metric_name, value in stats.items():
                            if isinstance(value, (int, float)):
                                metric = AnalysisMetric(
                                    analysis_id=analysis.id,
                                    metric_name=f"{col}_{metric_name}",
                                    metric_value=value,
                                    metric_category="statistical",
                                    calculation_method="pandas_describe",
                                )
                                self.db.add(metric)

            elif analysis.analysis_type == "trend":
                trends = results.get("trends", {})
                growth_rates = results.get("growth_rates", {})

                for col, trend_data in trends.items():
                    slope = trend_data.get("slope", 0)
                    metric = AnalysisMetric(
                        analysis_id=analysis.id,
                        metric_name=f"{col}_trend_slope",
                        metric_value=slope,
                        metric_category="trend",
                        calculation_method="linear_regression",
                    )
                    self.db.add(metric)

                for col, growth_rate in growth_rates.items():
                    metric = AnalysisMetric(
                        analysis_id=analysis.id,
                        metric_name=f"{col}_growth_rate",
                        metric_value=growth_rate,
                        metric_unit="percentage",
                        metric_category="growth",
                        calculation_method="period_over_period",
                    )
                    self.db.add(metric)

            self.db.commit()

        except Exception as e:
            logger.error(f"Error storing analysis metrics: {e}")

    async def _generate_report_content(
        self,
        report: Report,
        analysis: Optional[Analysis],
        template: Optional[ReportTemplate],
        report_data: ReportGenerate,
        current_user: User,
    ):
        """Generate comprehensive report content."""
        try:
            start_time = datetime.utcnow()

            # Initialize report content structure
            content = {
                "sections": [],
                "metadata": {
                    "generated_at": start_time.isoformat(),
                    "generated_by": current_user.full_name,
                    "report_type": report_data.report_type,
                    "audience": report_data.audience,
                },
            }

            # Generate executive summary if requested
            if report_data.include_executive_summary:
                executive_summary = await self._generate_executive_summary(
                    analysis, report_data
                )
                report.executive_summary = executive_summary

                content["sections"].append(
                    {
                        "type": "executive_summary",
                        "title": "Executive Summary",
                        "content": executive_summary,
                        "order": 1,
                    }
                )

            # Generate main content sections
            if analysis:
                await self._add_analysis_sections(content, analysis, report_data)

            # Add custom sections if provided
            if report_data.custom_sections:
                for i, section in enumerate(report_data.custom_sections):
                    content["sections"].append(
                        {**section, "order": len(content["sections"]) + 1}
                    )

            # Generate recommendations if requested
            if report_data.include_recommendations:
                recommendations = await self._generate_report_recommendations(
                    analysis, report_data
                )
                report.recommendations = recommendations

                content["sections"].append(
                    {
                        "type": "recommendations",
                        "title": "Recommendations",
                        "content": recommendations,
                        "order": len(content["sections"]) + 1,
                    }
                )

            # Generate key insights
            key_insights = await self._generate_key_insights(analysis, report_data)
            report.key_insights = key_insights

            # Update report with generated content
            report.content = content
            report.status = "completed"
            report.generated_at = datetime.utcnow()
            report.generation_time_ms = int(
                (report.generated_at - start_time).total_seconds() * 1000
            )
            report.ai_model_used = (
                "internal_nlg"  # Update when using external AI models
            )

            self.db.commit()

        except Exception as e:
            logger.error(f"Error generating report content: {e}")
            report.status = "failed"
            self.db.commit()
            raise

    async def _generate_executive_summary(
        self, analysis: Optional[Analysis], report_data: ReportGenerate
    ) -> str:
        """Generate AI-powered executive summary."""
        try:
            if not analysis:
                return "This report provides a comprehensive overview of the data analysis results."

            insights = analysis.insights or {}
            key_findings = insights.get("key_findings", [])

            # Create executive summary based on analysis type and findings
            summary_parts = []

            # Opening statement
            summary_parts.append(
                f"This {analysis.analysis_type} analysis of {analysis.name} reveals several key insights."
            )

            # Key findings (limit to top 3)
            if key_findings:
                summary_parts.append("Key findings include:")
                for finding in key_findings[:3]:
                    summary_parts.append(f"• {finding}")

            # Business implications based on audience
            if report_data.audience == "executive":
                summary_parts.append(
                    "These findings suggest strategic opportunities for data-driven decision making."
                )
            elif report_data.audience == "analyst":
                summary_parts.append(
                    "Further analysis is recommended to validate these patterns and explore causal relationships."
                )
            else:
                summary_parts.append(
                    "These insights provide valuable context for operational planning and performance monitoring."
                )

            return " ".join(summary_parts)

        except Exception as e:
            logger.error(f"Error generating executive summary: {e}")
            return "Executive summary generation encountered an error."

    async def _add_analysis_sections(
        self, content: Dict[str, Any], analysis: Analysis, report_data: ReportGenerate
    ):
        """Add analysis-specific sections to report content."""
        try:
            results = analysis.results or {}
            insights = analysis.insights or {}

            # Data Overview Section
            if "data_overview" in results:
                data_overview = results["data_overview"]
                content["sections"].append(
                    {
                        "type": "data_overview",
                        "title": "Data Overview",
                        "content": {
                            "summary": f"Dataset contains {data_overview.get('total_rows', 0):,} records across {data_overview.get('total_columns', 0)} variables",
                            "details": data_overview,
                        },
                        "order": len(content["sections"]) + 1,
                    }
                )

            # Analysis Results Section
            content["sections"].append(
                {
                    "type": "analysis_results",
                    "title": f"{analysis.analysis_type.title()} Analysis Results",
                    "content": {
                        "results": results,
                        "insights": insights.get("key_findings", []),
                    },
                    "order": len(content["sections"]) + 1,
                }
            )

            # Visualizations Section (placeholder for charts)
            content["sections"].append(
                {
                    "type": "visualizations",
                    "title": "Key Visualizations",
                    "content": {
                        "charts": await self._generate_chart_specifications(
                            analysis, results
                        )
                    },
                    "order": len(content["sections"]) + 1,
                }
            )

        except Exception as e:
            logger.error(f"Error adding analysis sections: {e}")

    async def _generate_chart_specifications(
        self, analysis: Analysis, results: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Generate chart specifications for visualizations."""
        try:
            charts = []

            if analysis.analysis_type == "descriptive":
                # Summary statistics chart
                summary_stats = results.get("summary_statistics", {})
                if summary_stats:
                    charts.append(
                        {
                            "type": "bar",
                            "title": "Summary Statistics",
                            "data": summary_stats,
                            "description": "Key statistical measures for numeric variables",
                        }
                    )

            elif analysis.analysis_type == "trend":
                # Trend lines chart
                trends = results.get("trends", {})
                if trends:
                    charts.append(
                        {
                            "type": "line",
                            "title": "Trend Analysis",
                            "data": trends,
                            "description": "Temporal trends in key variables",
                        }
                    )

            elif analysis.analysis_type == "correlation":
                # Correlation heatmap
                corr_matrix = results.get("correlation_matrix", {})
                if corr_matrix:
                    charts.append(
                        {
                            "type": "heatmap",
                            "title": "Correlation Matrix",
                            "data": corr_matrix,
                            "description": "Correlation relationships between variables",
                        }
                    )

            return charts

        except Exception as e:
            logger.error(f"Error generating chart specifications: {e}")
            return []

    async def _generate_report_recommendations(
        self, analysis: Optional[Analysis], report_data: ReportGenerate
    ) -> List[Dict[str, Any]]:
        """Generate actionable recommendations for the report."""
        try:
            recommendations = []

            if analysis and analysis.insights:
                base_recommendations = analysis.insights.get("recommendations", [])

                for i, rec in enumerate(base_recommendations):
                    recommendations.append(
                        {
                            "id": i + 1,
                            "title": f"Recommendation {i + 1}",
                            "description": rec,
                            "priority": "medium",
                            "category": "analysis",
                            "actionable": True,
                        }
                    )

            # Add audience-specific recommendations
            if report_data.audience == "executive":
                recommendations.append(
                    {
                        "id": len(recommendations) + 1,
                        "title": "Strategic Review",
                        "description": "Schedule quarterly reviews to monitor key performance indicators and trends",
                        "priority": "high",
                        "category": "strategic",
                        "actionable": True,
                    }
                )

            return recommendations

        except Exception as e:
            logger.error(f"Error generating report recommendations: {e}")
            return []

    async def _generate_key_insights(
        self, analysis: Optional[Analysis], report_data: ReportGenerate
    ) -> List[Dict[str, Any]]:
        """Generate key insights for the report."""
        try:
            insights = []

            if analysis and analysis.insights:
                key_findings = analysis.insights.get("key_findings", [])

                for i, finding in enumerate(key_findings):
                    insights.append(
                        {
                            "id": i + 1,
                            "type": "finding",
                            "title": f"Key Finding {i + 1}",
                            "description": finding,
                            "confidence": 0.8,  # Default confidence
                            "impact": "medium",
                        }
                    )

            return insights

        except Exception as e:
            logger.error(f"Error generating key insights: {e}")
            return []
