"""
JuliusAI Forecasting API Endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Optional, List
import logging
from uuid import UUID

from app.database import get_db
from app.schemas.forecast import (
    ForecastModel,
    ForecastModelCreate,
    ForecastModelUpdate,
    ForecastModelList,
    Forecast,
    ForecastCreate,
    ForecastUpdate,
    ForecastList,
    BacktestResult,
    BacktestCreate,
    BacktestResultList,
    ForecastScenario,
    ForecastScenarioCreate,
    ForecastScenarioUpdate,
    ForecastAlert,
)
from app.services.forecasting_service import ForecastingService
from app.core.deps import get_current_active_user
from app.models.user import User
from app.models.forecast import (
    ForecastModel as ForecastModelDB,
    Forecast as ForecastDB,
    BacktestResult as BacktestResultDB,
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/forecasting", tags=["Forecasting"])


# Forecast Model Endpoints
@router.post(
    "/models", response_model=ForecastModel, status_code=status.HTTP_201_CREATED
)
async def create_forecast_model(
    model_data: ForecastModelCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> ForecastModel:
    """
    Create a new forecast model.

    Args:
        model_data: Model configuration data
        current_user: Current authenticated user
        db: Database session

    Returns:
        ForecastModel: Created model instance
    """
    try:
        forecasting_service = ForecastingService(db)
        model = await forecasting_service.create_forecast_model(
            model_data, current_user
        )

        logger.info(f"Forecast model created by user {current_user.id}: {model.id}")
        return model

    except Exception as e:
        logger.error(f"Error creating forecast model: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create forecast model",
        )


@router.get("/models", response_model=ForecastModelList)
async def list_forecast_models(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    search: Optional[str] = Query(None, description="Search term"),
    model_type: Optional[str] = Query(None, description="Filter by model type"),
    status: Optional[str] = Query(None, description="Filter by status"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> ForecastModelList:
    """
    List forecast models for the current user's organization.

    Args:
        page: Page number
        size: Page size
        search: Search term for model names
        model_type: Filter by model type
        status: Filter by model status
        current_user: Current authenticated user
        db: Database session

    Returns:
        ForecastModelList: Paginated list of forecast models
    """
    try:
        query = db.query(ForecastModelDB).filter(
            ForecastModelDB.organization_id == current_user.organization_id,
            ForecastModelDB.is_active == True,
        )

        # Apply filters
        if search:
            query = query.filter(ForecastModelDB.name.ilike(f"%{search}%"))
        if model_type:
            query = query.filter(ForecastModelDB.model_type == model_type)
        if status:
            query = query.filter(ForecastModelDB.status == status)

        # Get total count
        total = query.count()

        # Apply pagination
        skip = (page - 1) * size
        models = (
            query.order_by(ForecastModelDB.created_at.desc())
            .offset(skip)
            .limit(size)
            .all()
        )

        pages = (total + size - 1) // size

        return ForecastModelList(
            models=models, total=total, page=page, size=size, pages=pages
        )

    except Exception as e:
        logger.error(f"Error listing forecast models: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve forecast models",
        )


@router.get("/models/{model_id}", response_model=ForecastModel)
async def get_forecast_model(
    model_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> ForecastModel:
    """
    Get a specific forecast model.

    Args:
        model_id: Model ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        ForecastModel: Model details
    """
    try:
        model = (
            db.query(ForecastModelDB)
            .filter(
                ForecastModelDB.id == model_id,
                ForecastModelDB.organization_id == current_user.organization_id,
            )
            .first()
        )

        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Forecast model not found"
            )

        return model

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting forecast model: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve forecast model",
        )


@router.put("/models/{model_id}", response_model=ForecastModel)
async def update_forecast_model(
    model_id: UUID,
    model_update: ForecastModelUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> ForecastModel:
    """
    Update a forecast model.

    Args:
        model_id: Model ID
        model_update: Model update data
        current_user: Current authenticated user
        db: Database session

    Returns:
        ForecastModel: Updated model
    """
    try:
        model = (
            db.query(ForecastModelDB)
            .filter(
                ForecastModelDB.id == model_id,
                ForecastModelDB.organization_id == current_user.organization_id,
            )
            .first()
        )

        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Forecast model not found"
            )

        # Update model fields
        update_data = model_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(model, field, value)

        db.commit()
        db.refresh(model)

        logger.info(f"Forecast model updated: {model.id}")
        return model

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating forecast model: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update forecast model",
        )


@router.post("/models/{model_id}/train", response_model=ForecastModel)
async def train_forecast_model(
    model_id: UUID,
    dataset_id: UUID,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> ForecastModel:
    """
    Train a forecast model with the specified dataset.

    Args:
        model_id: Model ID to train
        dataset_id: Dataset ID for training
        background_tasks: Background task manager
        current_user: Current authenticated user
        db: Database session

    Returns:
        ForecastModel: Model with training status
    """
    try:
        forecasting_service = ForecastingService(db)

        # Start training in background
        background_tasks.add_task(
            forecasting_service.train_model,
            str(model_id),
            str(dataset_id),
            current_user,
        )

        # Return model with updated status
        model = (
            db.query(ForecastModelDB)
            .filter(
                ForecastModelDB.id == model_id,
                ForecastModelDB.organization_id == current_user.organization_id,
            )
            .first()
        )

        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Forecast model not found"
            )

        logger.info(f"Training started for model {model_id}")
        return model

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting model training: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start model training",
        )


# Forecast Endpoints
@router.post("/forecasts", response_model=Forecast, status_code=status.HTTP_201_CREATED)
async def create_forecast(
    forecast_data: ForecastCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> Forecast:
    """
    Create a new forecast using a trained model.

    Args:
        forecast_data: Forecast configuration
        background_tasks: Background task manager
        current_user: Current authenticated user
        db: Database session

    Returns:
        Forecast: Created forecast instance
    """
    try:
        forecasting_service = ForecastingService(db)

        # Create forecast (this will start generation in background)
        forecast = await forecasting_service.create_forecast(
            forecast_data, current_user
        )

        logger.info(f"Forecast created by user {current_user.id}: {forecast.id}")
        return forecast

    except Exception as e:
        logger.error(f"Error creating forecast: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create forecast",
        )


@router.get("/forecasts", response_model=ForecastList)
async def list_forecasts(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    model_id: Optional[UUID] = Query(None, description="Filter by model ID"),
    status: Optional[str] = Query(None, description="Filter by status"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> ForecastList:
    """
    List forecasts for the current user's organization.

    Args:
        page: Page number
        size: Page size
        model_id: Filter by model ID
        status: Filter by forecast status
        current_user: Current authenticated user
        db: Database session

    Returns:
        ForecastList: Paginated list of forecasts
    """
    try:
        # Join with forecast models to filter by organization
        query = (
            db.query(ForecastDB)
            .join(ForecastModelDB)
            .filter(ForecastModelDB.organization_id == current_user.organization_id)
        )

        # Apply filters
        if model_id:
            query = query.filter(ForecastDB.model_id == model_id)
        if status:
            query = query.filter(ForecastDB.status == status)

        # Get total count
        total = query.count()

        # Apply pagination
        skip = (page - 1) * size
        forecasts = (
            query.order_by(ForecastDB.created_at.desc()).offset(skip).limit(size).all()
        )

        pages = (total + size - 1) // size

        return ForecastList(
            forecasts=forecasts, total=total, page=page, size=size, pages=pages
        )

    except Exception as e:
        logger.error(f"Error listing forecasts: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve forecasts",
        )


@router.get("/forecasts/{forecast_id}", response_model=Forecast)
async def get_forecast(
    forecast_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> Forecast:
    """
    Get a specific forecast.

    Args:
        forecast_id: Forecast ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        Forecast: Forecast details with results
    """
    try:
        forecast = (
            db.query(ForecastDB)
            .join(ForecastModelDB)
            .filter(
                ForecastDB.id == forecast_id,
                ForecastModelDB.organization_id == current_user.organization_id,
            )
            .first()
        )

        if not forecast:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Forecast not found"
            )

        return forecast

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting forecast: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve forecast",
        )


# Backtest Endpoints
@router.post(
    "/backtests", response_model=BacktestResult, status_code=status.HTTP_201_CREATED
)
async def create_backtest(
    backtest_data: BacktestCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> BacktestResult:
    """
    Create and run a backtest for a forecast model.

    Args:
        backtest_data: Backtest configuration
        background_tasks: Background task manager
        current_user: Current authenticated user
        db: Database session

    Returns:
        BacktestResult: Created backtest instance
    """
    try:
        forecasting_service = ForecastingService(db)

        # Create and run backtest
        backtest = await forecasting_service.run_backtest(backtest_data, current_user)

        logger.info(f"Backtest created by user {current_user.id}: {backtest.id}")
        return backtest

    except Exception as e:
        logger.error(f"Error creating backtest: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create backtest",
        )


@router.get("/backtests", response_model=BacktestResultList)
async def list_backtests(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    model_id: Optional[UUID] = Query(None, description="Filter by model ID"),
    status: Optional[str] = Query(None, description="Filter by status"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> BacktestResultList:
    """
    List backtest results for the current user's organization.

    Args:
        page: Page number
        size: Page size
        model_id: Filter by model ID
        status: Filter by backtest status
        current_user: Current authenticated user
        db: Database session

    Returns:
        BacktestResultList: Paginated list of backtest results
    """
    try:
        # Join with forecast models to filter by organization
        query = (
            db.query(BacktestResultDB)
            .join(ForecastModelDB)
            .filter(ForecastModelDB.organization_id == current_user.organization_id)
        )

        # Apply filters
        if model_id:
            query = query.filter(BacktestResultDB.model_id == model_id)
        if status:
            query = query.filter(BacktestResultDB.status == status)

        # Get total count
        total = query.count()

        # Apply pagination
        skip = (page - 1) * size
        results = (
            query.order_by(BacktestResultDB.created_at.desc())
            .offset(skip)
            .limit(size)
            .all()
        )

        pages = (total + size - 1) // size

        return BacktestResultList(
            results=results, total=total, page=page, size=size, pages=pages
        )

    except Exception as e:
        logger.error(f"Error listing backtests: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve backtest results",
        )


@router.get("/backtests/{backtest_id}", response_model=BacktestResult)
async def get_backtest(
    backtest_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> BacktestResult:
    """
    Get a specific backtest result.

    Args:
        backtest_id: Backtest ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        BacktestResult: Backtest details with performance metrics
    """
    try:
        backtest = (
            db.query(BacktestResultDB)
            .join(ForecastModelDB)
            .filter(
                BacktestResultDB.id == backtest_id,
                ForecastModelDB.organization_id == current_user.organization_id,
            )
            .first()
        )

        if not backtest:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Backtest result not found",
            )

        return backtest

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting backtest: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve backtest result",
        )


# Model Management Endpoints
@router.delete("/models/{model_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_forecast_model(
    model_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """
    Delete a forecast model (soft delete).

    Args:
        model_id: Model ID
        current_user: Current authenticated user
        db: Database session
    """
    try:
        model = (
            db.query(ForecastModelDB)
            .filter(
                ForecastModelDB.id == model_id,
                ForecastModelDB.organization_id == current_user.organization_id,
            )
            .first()
        )

        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Forecast model not found"
            )

        # Soft delete
        model.is_active = False
        db.commit()

        logger.info(f"Forecast model deleted: {model.id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting forecast model: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete forecast model",
        )


@router.get("/models/types", response_model=List[str])
async def get_model_types() -> List[str]:
    """
    Get available forecast model types.

    Returns:
        List[str]: Available model types
    """
    return ["prophet", "arima", "lstm", "linear_regression", "exponential_smoothing"]


@router.get("/models/{model_id}/metrics", response_model=dict)
async def get_model_metrics(
    model_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> dict:
    """
    Get validation metrics for a trained model.

    Args:
        model_id: Model ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        dict: Model validation metrics
    """
    try:
        model = (
            db.query(ForecastModelDB)
            .filter(
                ForecastModelDB.id == model_id,
                ForecastModelDB.organization_id == current_user.organization_id,
                ForecastModelDB.status == "trained",
            )
            .first()
        )

        if not model:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Trained forecast model not found",
            )

        return model.validation_metrics or {}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting model metrics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve model metrics",
        )
