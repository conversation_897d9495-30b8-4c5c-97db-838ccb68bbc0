"""
JuliusAI Forecasting Models
"""
import uuid
from sqlalchemy import <PERSON>umn, String, Integer, Float, Boolean, DateTime, Text, ForeignKey, DECIMAL, JSON as JSONB
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Dict, Any, Optional, List

from app.database import Base


class ForecastModel(Base):
    """Forecasting model configuration and metadata."""
    
    __tablename__ = "forecast_models"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    model_type = Column(String(50), nullable=False)  # prophet, arima, lstm, linear_regression
    target_column = Column(String(255), nullable=False)
    features = Column(JSONB)  # List of feature columns
    parameters = Column(JSONB)  # Model-specific parameters
    training_data_start = Column(DateTime(timezone=True))
    training_data_end = Column(DateTime(timezone=True))
    validation_metrics = Column(JSONB)  # MAPE, RMSE, MAE, etc.
    status = Column(String(50), default="created", nullable=False)  # created, training, trained, failed
    training_started_at = Column(DateTime(timezone=True))
    training_completed_at = Column(DateTime(timezone=True))
    model_file_path = Column(String(500))  # Path to serialized model
    error_message = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    organization = relationship("Organization")
    user = relationship("User")
    forecasts = relationship("Forecast", back_populates="model", cascade="all, delete-orphan")
    backtests = relationship("BacktestResult", back_populates="model", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<ForecastModel(id={self.id}, name='{self.name}', type='{self.model_type}')>"


class Forecast(Base):
    """Individual forecast results."""
    
    __tablename__ = "forecasts"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    model_id = Column(UUID(as_uuid=True), ForeignKey("forecast_models.id"), nullable=False)
    dataset_id = Column(UUID(as_uuid=True), ForeignKey("datasets.id"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    forecast_horizon = Column(Integer, nullable=False)  # Number of periods to forecast
    forecast_frequency = Column(String(20), nullable=False)  # daily, weekly, monthly, quarterly, yearly
    scenario_type = Column(String(50), default="base_case")  # base_case, best_case, worst_case
    confidence_level = Column(Float, default=0.95)  # Confidence interval level
    forecast_data = Column(JSONB, nullable=False)  # Forecast results with dates and values
    confidence_intervals = Column(JSONB)  # Upper and lower bounds
    feature_importance = Column(JSONB)  # Feature importance scores
    forecast_accuracy = Column(JSONB)  # Accuracy metrics if validation data available
    status = Column(String(50), default="pending", nullable=False)  # pending, running, completed, failed
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    error_message = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    model = relationship("ForecastModel", back_populates="forecasts")
    dataset = relationship("Dataset")
    
    def __repr__(self):
        return f"<Forecast(id={self.id}, name='{self.name}', horizon={self.forecast_horizon})>"


class BacktestResult(Base):
    """Backtesting results for model validation."""
    
    __tablename__ = "backtest_results"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    model_id = Column(UUID(as_uuid=True), ForeignKey("forecast_models.id"), nullable=False)
    dataset_id = Column(UUID(as_uuid=True), ForeignKey("datasets.id"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    backtest_start_date = Column(DateTime(timezone=True), nullable=False)
    backtest_end_date = Column(DateTime(timezone=True), nullable=False)
    forecast_horizon = Column(Integer, nullable=False)
    cross_validation_folds = Column(Integer, default=5)
    
    # Performance Metrics
    mape = Column(DECIMAL(10, 6))  # Mean Absolute Percentage Error
    rmse = Column(DECIMAL(15, 6))  # Root Mean Square Error
    mae = Column(DECIMAL(15, 6))   # Mean Absolute Error
    mse = Column(DECIMAL(15, 6))   # Mean Square Error
    r_squared = Column(DECIMAL(10, 6))  # R-squared
    directional_accuracy = Column(DECIMAL(10, 6))  # Percentage of correct direction predictions
    
    # Detailed Results
    predictions_vs_actual = Column(JSONB)  # Detailed prediction vs actual comparison
    residuals_analysis = Column(JSONB)  # Residuals statistics and patterns
    performance_by_period = Column(JSONB)  # Performance breakdown by time periods
    feature_stability = Column(JSONB)  # Feature importance stability across folds
    
    # Execution Details
    status = Column(String(50), default="pending", nullable=False)  # pending, running, completed, failed
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    execution_time_seconds = Column(Integer)
    error_message = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    model = relationship("ForecastModel", back_populates="backtests")
    dataset = relationship("Dataset")
    
    def __repr__(self):
        return f"<BacktestResult(id={self.id}, name='{self.name}', mape={self.mape})>"


class ForecastScenario(Base):
    """Scenario-based forecasting configurations."""
    
    __tablename__ = "forecast_scenarios"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    scenario_type = Column(String(50), nullable=False)  # optimistic, pessimistic, realistic, custom
    
    # Scenario Parameters
    growth_assumptions = Column(JSONB)  # Growth rate assumptions for different metrics
    external_factors = Column(JSONB)  # External economic factors and their impacts
    seasonal_adjustments = Column(JSONB)  # Seasonal pattern modifications
    risk_adjustments = Column(JSONB)  # Risk-based adjustments to forecasts
    
    # Scenario Metadata
    probability_weight = Column(DECIMAL(5, 4), default=0.33)  # Probability weight for scenario
    confidence_level = Column(DECIMAL(5, 4), default=0.80)  # Confidence in scenario assumptions
    review_date = Column(DateTime(timezone=True))  # When scenario should be reviewed
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    organization = relationship("Organization")
    user = relationship("User")
    
    def __repr__(self):
        return f"<ForecastScenario(id={self.id}, name='{self.name}', type='{self.scenario_type}')>"


class ForecastAlert(Base):
    """Alerts for forecast anomalies and significant changes."""
    
    __tablename__ = "forecast_alerts"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    forecast_id = Column(UUID(as_uuid=True), ForeignKey("forecasts.id"), nullable=False)
    alert_type = Column(String(50), nullable=False)  # accuracy_drop, significant_change, anomaly
    severity = Column(String(20), default="medium")  # low, medium, high, critical
    title = Column(String(255), nullable=False)
    message = Column(Text, nullable=False)
    threshold_value = Column(DECIMAL(15, 6))
    actual_value = Column(DECIMAL(15, 6))
    deviation_percentage = Column(DECIMAL(10, 6))
    alert_data = Column(JSONB)  # Additional alert-specific data
    is_acknowledged = Column(Boolean, default=False)
    acknowledged_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    acknowledged_at = Column(DateTime(timezone=True))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    forecast = relationship("Forecast")
    acknowledged_by_user = relationship("User", foreign_keys=[acknowledged_by])
    
    def __repr__(self):
        return f"<ForecastAlert(id={self.id}, type='{self.alert_type}', severity='{self.severity}')>"
