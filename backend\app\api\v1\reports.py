"""
JuliusAI Reports and Analysis API Endpoints
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from sqlalchemy.orm import Session
from typing import Optional, List
import logging
from uuid import UUID

from app.database import get_db
from app.schemas.report import (
    Analysis,
    AnalysisCreate,
    AnalysisUpdate,
    AnalysisList,
    Report,
    ReportCreate,
    ReportGenerate,
    ReportUpdate,
    ReportList,
    ReportTemplate,
    ReportTemplateCreate,
    ReportTemplateUpdate,
    ReportTemplateList,
    ReportExport,
    ReportExportRequest,
    AnalysisSummary,
    ReportSummary,
)
from app.services.report_service import ReportService
from app.core.deps import get_current_active_user
from app.models.user import User
from app.models.report import (
    Analysis as AnalysisDB,
    Report as ReportDB,
    ReportTemplate as ReportTemplateDB,
    ReportExport as ReportExportDB,
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/reports", tags=["Reports & Analysis"])


# Analysis Endpoints
@router.post("/analyses", response_model=Analysis, status_code=status.HTTP_201_CREATED)
async def create_analysis(
    analysis_data: AnalysisCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> Analysis:
    """
    Create a new data analysis.

    Args:
        analysis_data: Analysis configuration data
        background_tasks: Background task manager
        current_user: Current authenticated user
        db: Database session

    Returns:
        Analysis: Created analysis instance
    """
    try:
        report_service = ReportService(db)
        analysis = await report_service.create_analysis(analysis_data, current_user)

        logger.info(f"Analysis created by user {current_user.id}: {analysis.id}")
        return analysis

    except Exception as e:
        logger.error(f"Error creating analysis: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create analysis",
        )


@router.get("/analyses", response_model=AnalysisList)
async def list_analyses(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    search: Optional[str] = Query(None, description="Search term"),
    analysis_type: Optional[str] = Query(None, description="Filter by analysis type"),
    status: Optional[str] = Query(None, description="Filter by status"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> AnalysisList:
    """
    List analyses for the current user's organization.

    Args:
        page: Page number
        size: Page size
        search: Search term for analysis names
        analysis_type: Filter by analysis type
        status: Filter by analysis status
        current_user: Current authenticated user
        db: Database session

    Returns:
        AnalysisList: Paginated list of analyses
    """
    try:
        query = db.query(AnalysisDB).filter(
            AnalysisDB.organization_id == current_user.organization_id
        )

        # Apply filters
        if search:
            query = query.filter(AnalysisDB.name.ilike(f"%{search}%"))
        if analysis_type:
            query = query.filter(AnalysisDB.analysis_type == analysis_type)
        if status:
            query = query.filter(AnalysisDB.status == status)

        # Get total count
        total = query.count()

        # Apply pagination
        skip = (page - 1) * size
        analyses = (
            query.order_by(AnalysisDB.created_at.desc()).offset(skip).limit(size).all()
        )

        pages = (total + size - 1) // size

        return AnalysisList(
            analyses=analyses, total=total, page=page, size=size, pages=pages
        )

    except Exception as e:
        logger.error(f"Error listing analyses: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve analyses",
        )


@router.get("/analyses/{analysis_id}", response_model=Analysis)
async def get_analysis(
    analysis_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> Analysis:
    """
    Get a specific analysis.

    Args:
        analysis_id: Analysis ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        Analysis: Analysis details with results
    """
    try:
        analysis = (
            db.query(AnalysisDB)
            .filter(
                AnalysisDB.id == analysis_id,
                AnalysisDB.organization_id == current_user.organization_id,
            )
            .first()
        )

        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Analysis not found"
            )

        return analysis

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting analysis: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve analysis",
        )


@router.put("/analyses/{analysis_id}", response_model=Analysis)
async def update_analysis(
    analysis_id: UUID,
    analysis_update: AnalysisUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> Analysis:
    """
    Update an analysis.

    Args:
        analysis_id: Analysis ID
        analysis_update: Analysis update data
        current_user: Current authenticated user
        db: Database session

    Returns:
        Analysis: Updated analysis
    """
    try:
        analysis = (
            db.query(AnalysisDB)
            .filter(
                AnalysisDB.id == analysis_id,
                AnalysisDB.organization_id == current_user.organization_id,
            )
            .first()
        )

        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Analysis not found"
            )

        # Update analysis fields
        update_data = analysis_update.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(analysis, field, value)

        db.commit()
        db.refresh(analysis)

        logger.info(f"Analysis updated: {analysis.id}")
        return analysis

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating analysis: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update analysis",
        )


# Report Generation Endpoints
@router.post("/generate", response_model=Report, status_code=status.HTTP_201_CREATED)
async def generate_report(
    report_data: ReportGenerate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> Report:
    """
    Generate a comprehensive report with AI-powered content.

    Args:
        report_data: Report generation configuration
        background_tasks: Background task manager
        current_user: Current authenticated user
        db: Database session

    Returns:
        Report: Generated report instance
    """
    try:
        report_service = ReportService(db)
        report = await report_service.generate_report(report_data, current_user)

        logger.info(f"Report generated by user {current_user.id}: {report.id}")
        return report

    except Exception as e:
        logger.error(f"Error generating report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to generate report",
        )


@router.get("/", response_model=ReportList)
async def list_reports(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    search: Optional[str] = Query(None, description="Search term"),
    report_type: Optional[str] = Query(None, description="Filter by report type"),
    audience: Optional[str] = Query(None, description="Filter by audience"),
    status: Optional[str] = Query(None, description="Filter by status"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> ReportList:
    """
    List reports for the current user's organization.

    Args:
        page: Page number
        size: Page size
        search: Search term for report titles
        report_type: Filter by report type
        audience: Filter by target audience
        status: Filter by report status
        current_user: Current authenticated user
        db: Database session

    Returns:
        ReportList: Paginated list of reports
    """
    try:
        query = db.query(ReportDB).filter(
            ReportDB.organization_id == current_user.organization_id
        )

        # Apply filters
        if search:
            query = query.filter(ReportDB.title.ilike(f"%{search}%"))
        if report_type:
            query = query.filter(ReportDB.report_type == report_type)
        if audience:
            query = query.filter(ReportDB.audience == audience)
        if status:
            query = query.filter(ReportDB.status == status)

        # Get total count
        total = query.count()

        # Apply pagination
        skip = (page - 1) * size
        reports = (
            query.order_by(ReportDB.created_at.desc()).offset(skip).limit(size).all()
        )

        pages = (total + size - 1) // size

        return ReportList(
            reports=reports, total=total, page=page, size=size, pages=pages
        )

    except Exception as e:
        logger.error(f"Error listing reports: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve reports",
        )


@router.get("/{report_id}", response_model=Report)
async def get_report(
    report_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> Report:
    """
    Get a specific report.

    Args:
        report_id: Report ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        Report: Report details with content
    """
    try:
        report = (
            db.query(ReportDB)
            .filter(
                ReportDB.id == report_id,
                ReportDB.organization_id == current_user.organization_id,
            )
            .first()
        )

        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Report not found"
            )

        return report

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting report: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve report",
        )


# Report Template Endpoints
@router.post(
    "/templates", response_model=ReportTemplate, status_code=status.HTTP_201_CREATED
)
async def create_report_template(
    template_data: ReportTemplateCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> ReportTemplate:
    """
    Create a new report template.

    Args:
        template_data: Template configuration data
        current_user: Current authenticated user
        db: Database session

    Returns:
        ReportTemplate: Created template instance
    """
    try:
        db_template = ReportTemplateDB(
            organization_id=current_user.organization_id,
            user_id=current_user.id,
            name=template_data.name,
            description=template_data.description,
            template_type=template_data.template_type,
            audience=template_data.audience,
            category=template_data.category,
            template_config=template_data.template_config,
            sections=template_data.sections,
            styling=template_data.styling or {},
            default_parameters=template_data.default_parameters or {},
            is_public=template_data.is_public,
        )

        db.add(db_template)
        db.commit()
        db.refresh(db_template)

        logger.info(
            f"Report template created by user {current_user.id}: {db_template.id}"
        )
        return db_template

    except Exception as e:
        logger.error(f"Error creating report template: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create report template",
        )


@router.get("/templates", response_model=ReportTemplateList)
async def list_report_templates(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    template_type: Optional[str] = Query(None, description="Filter by template type"),
    audience: Optional[str] = Query(None, description="Filter by audience"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> ReportTemplateList:
    """
    List report templates for the current user's organization.

    Args:
        page: Page number
        size: Page size
        template_type: Filter by template type
        audience: Filter by target audience
        current_user: Current authenticated user
        db: Database session

    Returns:
        ReportTemplateList: Paginated list of templates
    """
    try:
        query = db.query(ReportTemplateDB).filter(
            ReportTemplateDB.organization_id == current_user.organization_id,
            ReportTemplateDB.is_active == True,
        )

        # Apply filters
        if template_type:
            query = query.filter(ReportTemplateDB.template_type == template_type)
        if audience:
            query = query.filter(ReportTemplateDB.audience == audience)

        # Get total count
        total = query.count()

        # Apply pagination
        skip = (page - 1) * size
        templates = (
            query.order_by(ReportTemplateDB.created_at.desc())
            .offset(skip)
            .limit(size)
            .all()
        )

        pages = (total + size - 1) // size

        return ReportTemplateList(
            templates=templates, total=total, page=page, size=size, pages=pages
        )

    except Exception as e:
        logger.error(f"Error listing report templates: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve report templates",
        )


@router.get("/templates/{template_id}", response_model=ReportTemplate)
async def get_report_template(
    template_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> ReportTemplate:
    """
    Get a specific report template.

    Args:
        template_id: Template ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        ReportTemplate: Template details
    """
    try:
        template = (
            db.query(ReportTemplateDB)
            .filter(
                ReportTemplateDB.id == template_id,
                ReportTemplateDB.organization_id == current_user.organization_id,
            )
            .first()
        )

        if not template:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Report template not found",
            )

        return template

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting report template: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve report template",
        )


# Export Endpoints
@router.post(
    "/{report_id}/export",
    response_model=ReportExport,
    status_code=status.HTTP_201_CREATED,
)
async def export_report(
    report_id: UUID,
    export_request: ReportExportRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> ReportExport:
    """
    Export a report to the specified format.

    Args:
        report_id: Report ID to export
        export_request: Export configuration
        background_tasks: Background task manager
        current_user: Current authenticated user
        db: Database session

    Returns:
        ReportExport: Export job details
    """
    try:
        # Verify report exists and user has access
        report = (
            db.query(ReportDB)
            .filter(
                ReportDB.id == report_id,
                ReportDB.organization_id == current_user.organization_id,
            )
            .first()
        )

        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Report not found"
            )

        # Create export job
        export_job = ReportExportDB(
            report_id=report_id,
            user_id=current_user.id,
            export_format=export_request.export_format,
            export_parameters=export_request.export_parameters or {},
            file_path="",  # Will be set when export completes
            status="pending",
        )

        db.add(export_job)
        db.commit()
        db.refresh(export_job)

        # Start export in background
        # background_tasks.add_task(export_service.export_report, export_job.id)

        logger.info(f"Report export started: {export_job.id}")
        return export_job

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting report export: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to start report export",
        )


@router.get("/{report_id}/exports", response_model=List[ReportExport])
async def list_report_exports(
    report_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
) -> List[ReportExport]:
    """
    List exports for a specific report.

    Args:
        report_id: Report ID
        current_user: Current authenticated user
        db: Database session

    Returns:
        List[ReportExport]: List of export jobs
    """
    try:
        # Verify report exists and user has access
        report = (
            db.query(ReportDB)
            .filter(
                ReportDB.id == report_id,
                ReportDB.organization_id == current_user.organization_id,
            )
            .first()
        )

        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Report not found"
            )

        exports = (
            db.query(ReportExportDB)
            .filter(
                ReportExportDB.report_id == report_id,
                ReportExportDB.user_id == current_user.id,
            )
            .order_by(ReportExportDB.created_at.desc())
            .all()
        )

        return exports

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error listing report exports: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve report exports",
        )


# Utility Endpoints
@router.get("/analysis-types", response_model=List[str])
async def get_analysis_types() -> List[str]:
    """
    Get available analysis types.

    Returns:
        List[str]: Available analysis types
    """
    return ["descriptive", "trend", "forecast", "anomaly", "correlation", "custom"]


@router.get("/report-types", response_model=List[str])
async def get_report_types() -> List[str]:
    """
    Get available report types.

    Returns:
        List[str]: Available report types
    """
    return ["analysis", "forecast", "summary", "custom"]


@router.get("/audiences", response_model=List[str])
async def get_audiences() -> List[str]:
    """
    Get available audience types.

    Returns:
        List[str]: Available audience types
    """
    return ["executive", "analyst", "manager", "stakeholder"]


@router.delete("/analyses/{analysis_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_analysis(
    analysis_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """
    Delete an analysis.

    Args:
        analysis_id: Analysis ID
        current_user: Current authenticated user
        db: Database session
    """
    try:
        analysis = (
            db.query(AnalysisDB)
            .filter(
                AnalysisDB.id == analysis_id,
                AnalysisDB.organization_id == current_user.organization_id,
            )
            .first()
        )

        if not analysis:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Analysis not found"
            )

        db.delete(analysis)
        db.commit()

        logger.info(f"Analysis deleted: {analysis_id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting analysis: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete analysis",
        )


@router.delete("/{report_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_report(
    report_id: UUID,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
):
    """
    Delete a report.

    Args:
        report_id: Report ID
        current_user: Current authenticated user
        db: Database session
    """
    try:
        report = (
            db.query(ReportDB)
            .filter(
                ReportDB.id == report_id,
                ReportDB.organization_id == current_user.organization_id,
            )
            .first()
        )

        if not report:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Report not found"
            )

        db.delete(report)
        db.commit()

        logger.info(f"Report deleted: {report_id}")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting report: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete report",
        )
