"""
JuliusAI Enhanced Security API Endpoints
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query, Request
from sqlalchemy.orm import Session
from typing import Optional, List
import logging
from uuid import UUID

from app.database import get_db
from app.schemas.security import (
    Role, RoleCreate, RoleUpdate, RoleList,
    Permission, PermissionList,
    UserRole, UserRoleCreate, UserRoleUpdate,
    MFAMethod, MFAMethodCreate, MFAMethodUpdate, MFASetupResponse,
    MFAVerificationRequest, MFAVerificationResponse,
    SecurityEvent, SecurityEventList,
    LoginAttempt, LoginAttemptList,
    SecurityPolicy, SecurityPolicyCreate, SecurityPolicyUpdate,
    AccessRequest, AccessRequestCreate, AccessRequestUpdate, AccessRequestList,
    SecurityDashboard, RiskAssessment
)
from app.services.security_service import SecurityService
from app.core.deps import get_current_active_user, require_role
from app.models.user import User
from app.models.security import (
    Role as RoleDB, Permission as PermissionDB, User<PERSON>ole as UserRoleDB,
    MFAMethod as MFAMethodDB, SecurityEvent as SecurityEventDB,
    LoginAttempt as LoginAttemptDB, SecurityPolicy as SecurityPolicyDB,
    AccessRequest as AccessRequestDB
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/security", tags=["Security Management"])


# Role Management Endpoints
@router.post("/roles", response_model=Role, status_code=status.HTTP_201_CREATED)
async def create_role(
    role_data: RoleCreate,
    current_user: User = Depends(require_role("admin")),
    db: Session = Depends(get_db)
) -> Role:
    """
    Create a new role with permissions.
    
    Args:
        role_data: Role configuration
        current_user: Current authenticated user (admin required)
        db: Database session
        
    Returns:
        Role: Created role instance
    """
    try:
        security_service = SecurityService(db)
        role = await security_service.create_role(role_data, current_user)
        
        logger.info(f"Role created by user {current_user.id}: {role.id}")
        return role
        
    except Exception as e:
        logger.error(f"Error creating role: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create role"
        )


@router.get("/roles", response_model=RoleList)
async def list_roles(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    search: Optional[str] = Query(None, description="Search term"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    current_user: User = Depends(require_role("manager")),
    db: Session = Depends(get_db)
) -> RoleList:
    """
    List roles for the current user's organization.
    
    Args:
        page: Page number
        size: Page size
        search: Search term for role names
        is_active: Filter by active status
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        RoleList: Paginated list of roles
    """
    try:
        query = db.query(RoleDB).filter(
            RoleDB.organization_id == current_user.organization_id
        )
        
        # Apply filters
        if search:
            query = query.filter(RoleDB.display_name.ilike(f"%{search}%"))
        if is_active is not None:
            query = query.filter(RoleDB.is_active == is_active)
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        skip = (page - 1) * size
        roles = query.order_by(RoleDB.priority.desc()).offset(skip).limit(size).all()
        
        pages = (total + size - 1) // size
        
        return RoleList(
            roles=roles,
            total=total,
            page=page,
            size=size,
            pages=pages
        )
        
    except Exception as e:
        logger.error(f"Error listing roles: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve roles"
        )


@router.get("/permissions", response_model=PermissionList)
async def list_permissions(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    category: Optional[str] = Query(None, description="Filter by category"),
    current_user: User = Depends(require_role("manager")),
    db: Session = Depends(get_db)
) -> PermissionList:
    """
    List available permissions.
    
    Args:
        page: Page number
        size: Page size
        category: Filter by permission category
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        PermissionList: Paginated list of permissions
    """
    try:
        query = db.query(PermissionDB).filter(PermissionDB.is_active == True)
        
        # Apply filters
        if category:
            query = query.filter(PermissionDB.category == category)
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        skip = (page - 1) * size
        permissions = query.order_by(PermissionDB.category, PermissionDB.name).offset(skip).limit(size).all()
        
        pages = (total + size - 1) // size
        
        return PermissionList(
            permissions=permissions,
            total=total,
            page=page,
            size=size,
            pages=pages
        )
        
    except Exception as e:
        logger.error(f"Error listing permissions: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve permissions"
        )


# User Role Management
@router.post("/user-roles", response_model=UserRole, status_code=status.HTTP_201_CREATED)
async def assign_user_role(
    user_role_data: UserRoleCreate,
    current_user: User = Depends(require_role("admin")),
    db: Session = Depends(get_db)
) -> UserRole:
    """
    Assign a role to a user.
    
    Args:
        user_role_data: User role assignment data
        current_user: Current authenticated user (admin required)
        db: Database session
        
    Returns:
        UserRole: Created user role assignment
    """
    try:
        security_service = SecurityService(db)
        user_role = await security_service.assign_role_to_user(user_role_data, current_user)
        
        logger.info(f"Role assigned by user {current_user.id}: {user_role.id}")
        return user_role
        
    except Exception as e:
        logger.error(f"Error assigning role: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to assign role"
        )


@router.get("/user-roles/{user_id}")
async def get_user_roles(
    user_id: UUID,
    current_user: User = Depends(require_role("manager")),
    db: Session = Depends(get_db)
) -> List[UserRole]:
    """
    Get roles assigned to a specific user.
    
    Args:
        user_id: Target user ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List[UserRole]: User's role assignments
    """
    try:
        # Verify user exists and is in same organization
        target_user = db.query(User).filter(
            User.id == user_id,
            User.organization_id == current_user.organization_id
        ).first()
        
        if not target_user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        user_roles = db.query(UserRoleDB).filter(
            UserRoleDB.user_id == user_id,
            UserRoleDB.is_active == True
        ).all()
        
        return user_roles
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting user roles: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve user roles"
        )


# MFA Management
@router.post("/mfa/setup", response_model=MFASetupResponse)
async def setup_mfa(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> MFASetupResponse:
    """
    Set up TOTP-based MFA for the current user.
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        MFASetupResponse: MFA setup data including QR code
    """
    try:
        security_service = SecurityService(db)
        setup_response = await security_service.setup_totp_mfa(current_user)
        
        logger.info(f"MFA setup initiated for user {current_user.id}")
        return setup_response
        
    except Exception as e:
        logger.error(f"Error setting up MFA: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to setup MFA"
        )


@router.post("/mfa/verify", response_model=MFAVerificationResponse)
async def verify_mfa(
    verification_request: MFAVerificationRequest,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> MFAVerificationResponse:
    """
    Verify MFA code.
    
    Args:
        verification_request: MFA verification data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        MFAVerificationResponse: Verification result
    """
    try:
        security_service = SecurityService(db)
        verification_response = await security_service.verify_mfa_code(verification_request, current_user)
        
        logger.info(f"MFA verification attempted for user {current_user.id}")
        return verification_response
        
    except Exception as e:
        logger.error(f"Error verifying MFA: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to verify MFA"
        )


@router.get("/mfa/methods")
async def get_mfa_methods(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> List[MFAMethod]:
    """
    Get MFA methods for the current user.
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List[MFAMethod]: User's MFA methods
    """
    try:
        mfa_methods = db.query(MFAMethodDB).filter(
            MFAMethodDB.user_id == current_user.id,
            MFAMethodDB.is_active == True
        ).all()
        
        return mfa_methods
        
    except Exception as e:
        logger.error(f"Error getting MFA methods: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve MFA methods"
        )


# Security Events and Audit Logs
@router.get("/events", response_model=SecurityEventList)
async def list_security_events(
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    event_type: Optional[str] = Query(None, description="Filter by event type"),
    severity: Optional[str] = Query(None, description="Filter by severity"),
    user_id: Optional[UUID] = Query(None, description="Filter by user ID"),
    current_user: User = Depends(require_role("admin")),
    db: Session = Depends(get_db)
) -> SecurityEventList:
    """
    List security events for audit purposes.
    
    Args:
        page: Page number
        size: Page size
        event_type: Filter by event type
        severity: Filter by severity level
        user_id: Filter by user ID
        current_user: Current authenticated user (admin required)
        db: Database session
        
    Returns:
        SecurityEventList: Paginated list of security events
    """
    try:
        query = db.query(SecurityEventDB).filter(
            SecurityEventDB.organization_id == current_user.organization_id
        )
        
        # Apply filters
        if event_type:
            query = query.filter(SecurityEventDB.event_type == event_type)
        if severity:
            query = query.filter(SecurityEventDB.severity == severity)
        if user_id:
            query = query.filter(SecurityEventDB.user_id == user_id)
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        skip = (page - 1) * size
        events = query.order_by(SecurityEventDB.timestamp.desc()).offset(skip).limit(size).all()
        
        pages = (total + size - 1) // size
        
        return SecurityEventList(
            events=events,
            total=total,
            page=page,
            size=size,
            pages=pages
        )
        
    except Exception as e:
        logger.error(f"Error listing security events: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve security events"
        )


# Security Dashboard
@router.get("/dashboard", response_model=SecurityDashboard)
async def get_security_dashboard(
    current_user: User = Depends(require_role("manager")),
    db: Session = Depends(get_db)
) -> SecurityDashboard:
    """
    Get security dashboard summary.
    
    Args:
        current_user: Current authenticated user (manager required)
        db: Database session
        
    Returns:
        SecurityDashboard: Security metrics and summary
    """
    try:
        security_service = SecurityService(db)
        dashboard = await security_service.get_security_dashboard(str(current_user.organization_id))
        
        return dashboard
        
    except Exception as e:
        logger.error(f"Error getting security dashboard: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve security dashboard"
        )


@router.get("/risk-assessment", response_model=RiskAssessment)
async def get_risk_assessment(
    current_user: User = Depends(require_role("admin")),
    db: Session = Depends(get_db)
) -> RiskAssessment:
    """
    Get organization risk assessment.
    
    Args:
        current_user: Current authenticated user (admin required)
        db: Database session
        
    Returns:
        RiskAssessment: Risk analysis and recommendations
    """
    try:
        security_service = SecurityService(db)
        risk_assessment = await security_service.assess_organization_risk(str(current_user.organization_id))
        
        return risk_assessment
        
    except Exception as e:
        logger.error(f"Error getting risk assessment: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve risk assessment"
        )
