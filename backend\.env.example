# JuliusAI Backend Environment Variables

# Database Configuration
DATABASE_URL=postgresql://postgres:password@localhost:5432/juliusai_dev
TEST_DATABASE_URL=postgresql://postgres:password@localhost:5432/juliusai_test

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Security Configuration
SECRET_KEY=your-super-secret-key-change-this-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS Configuration
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:5173"]

# File Upload Configuration
MAX_FILE_SIZE_MB=500
ALLOWED_FILE_TYPES=["csv", "xlsx", "xls"]
UPLOAD_DIR=./uploads

# External API Keys (Optional for MVP)
OPENAI_API_KEY=your-openai-api-key
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1

# Email Configuration (Optional for MVP)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Development Configuration
DEBUG=True
LOG_LEVEL=DEBUG
ENVIRONMENT=development

# Celery Configuration
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
