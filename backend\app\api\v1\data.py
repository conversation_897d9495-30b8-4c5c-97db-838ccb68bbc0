"""
JuliusAI Data Management API Endpoints
"""
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, Query
from sqlalchemy.orm import Session
from typing import Optional, List
import logging
import json

from app.database import get_db
from app.schemas.data import (
    Dataset, DatasetList, DatasetUpdate, DatasetUpload, 
    FileUploadResponse, DataPreview, DataColumn
)
from app.services.data_service import DataService
from app.core.deps import get_current_active_user
from app.models.user import User

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/data", tags=["Data Management"])


@router.post("/upload", response_model=FileUploadResponse, status_code=status.HTTP_201_CREATED)
async def upload_dataset(
    file: UploadFile = File(..., description="CSV or Excel file to upload"),
    name: Optional[str] = Form(None, description="Dataset name"),
    description: Optional[str] = Form(None, description="Dataset description"),
    tags: Optional[str] = Form(None, description="Comma-separated tags"),
    auto_process: bool = Form(True, description="Automatically process the file"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> FileUploadResponse:
    """
    Upload a new dataset file (CSV or Excel).
    
    Args:
        file: File to upload
        name: Optional dataset name (defaults to filename)
        description: Optional dataset description
        tags: Optional comma-separated tags
        auto_process: Whether to automatically process the file
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        FileUploadResponse: Upload result with dataset ID
    """
    try:
        data_service = DataService(db)
        
        # Parse tags
        tag_list = []
        if tags:
            tag_list = [tag.strip() for tag in tags.split(",") if tag.strip()]
        
        upload_data = DatasetUpload(
            name=name,
            description=description,
            tags=tag_list,
            auto_process=auto_process
        )
        
        result = await data_service.upload_dataset(file, upload_data, current_user)
        
        logger.info(f"Dataset uploaded by user {current_user.id}: {result.dataset_id}")
        return result
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Upload endpoint error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Upload failed"
        )


@router.get("/datasets", response_model=DatasetList)
async def get_datasets(
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(20, ge=1, le=100, description="Items per page"),
    status: Optional[str] = Query(None, description="Filter by status"),
    search: Optional[str] = Query(None, description="Search in dataset names"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> DatasetList:
    """
    Get list of datasets for current user's organization.
    
    Args:
        page: Page number (1-based)
        limit: Number of items per page
        status: Filter by dataset status
        search: Search term for dataset names
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        DatasetList: Paginated list of datasets
    """
    try:
        data_service = DataService(db)
        
        skip = (page - 1) * limit
        datasets, total_count = data_service.get_datasets(
            current_user=current_user,
            skip=skip,
            limit=limit,
            status_filter=status,
            search=search
        )
        
        # Calculate pagination info
        total_pages = (total_count + limit - 1) // limit
        has_next = page < total_pages
        has_prev = page > 1
        
        return DatasetList(
            data=datasets,
            total=total_count,
            page=page,
            limit=limit,
            pages=total_pages,
            has_next=has_next,
            has_prev=has_prev
        )
        
    except Exception as e:
        logger.error(f"Get datasets error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve datasets"
        )


@router.get("/datasets/{dataset_id}", response_model=Dataset)
async def get_dataset(
    dataset_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Dataset:
    """
    Get dataset by ID.
    
    Args:
        dataset_id: Dataset ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dataset: Dataset information
    """
    try:
        data_service = DataService(db)
        dataset = data_service.get_dataset(dataset_id, current_user)
        
        return dataset
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get dataset error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dataset"
        )


@router.put("/datasets/{dataset_id}", response_model=Dataset)
async def update_dataset(
    dataset_id: str,
    update_data: DatasetUpdate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> Dataset:
    """
    Update dataset metadata.
    
    Args:
        dataset_id: Dataset ID
        update_data: Update data
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        Dataset: Updated dataset
    """
    try:
        data_service = DataService(db)
        dataset = data_service.update_dataset(dataset_id, update_data, current_user)
        
        logger.info(f"Dataset updated by user {current_user.id}: {dataset_id}")
        return dataset
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Update dataset error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update dataset"
        )


@router.delete("/datasets/{dataset_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_dataset(
    dataset_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Delete dataset and associated file.
    
    Args:
        dataset_id: Dataset ID
        current_user: Current authenticated user
        db: Database session
    """
    try:
        data_service = DataService(db)
        await data_service.delete_dataset(dataset_id, current_user)
        
        logger.info(f"Dataset deleted by user {current_user.id}: {dataset_id}")
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Delete dataset error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete dataset"
        )


@router.get("/datasets/{dataset_id}/preview", response_model=DataPreview)
async def get_dataset_preview(
    dataset_id: str,
    rows: int = Query(10, ge=1, le=100, description="Number of rows to preview"),
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> DataPreview:
    """
    Get preview of dataset data.
    
    Args:
        dataset_id: Dataset ID
        rows: Number of rows to preview
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        DataPreview: Data preview with sample rows and statistics
    """
    try:
        data_service = DataService(db)
        preview = data_service.get_data_preview(dataset_id, current_user, rows)
        
        return preview
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get dataset preview error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to preview dataset"
        )


@router.get("/datasets/{dataset_id}/columns", response_model=List[DataColumn])
async def get_dataset_columns(
    dataset_id: str,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
) -> List[DataColumn]:
    """
    Get dataset column information.
    
    Args:
        dataset_id: Dataset ID
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        List[DataColumn]: List of dataset columns
    """
    try:
        data_service = DataService(db)
        dataset = data_service.get_dataset(dataset_id, current_user)
        
        return dataset.columns
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Get dataset columns error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve dataset columns"
        )


@router.get("/storage-usage")
async def get_storage_usage(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """
    Get storage usage for current organization.
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        dict: Storage usage information
    """
    try:
        data_service = DataService(db)
        usage = data_service.file_service.get_storage_usage(str(current_user.organization_id))
        
        # Get organization limits
        organization = current_user.organization
        storage_limit_bytes = organization.storage_limit_gb * 1024 * 1024 * 1024
        
        usage.update({
            "limit_gb": organization.storage_limit_gb,
            "limit_bytes": storage_limit_bytes,
            "usage_percentage": round((usage["total_size"] / storage_limit_bytes) * 100, 2) if storage_limit_bytes > 0 else 0
        })
        
        return usage
        
    except Exception as e:
        logger.error(f"Get storage usage error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve storage usage"
        )
