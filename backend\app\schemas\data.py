"""
JuliusAI Data Management Schemas
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from uuid import UUID
import re


class DataColumnBase(BaseModel):
    """Base data column schema."""
    name: str = Field(..., min_length=1, max_length=255)
    data_type: str = Field(..., regex="^(integer|decimal|text|date|datetime|boolean|json)$")
    is_numeric: bool = False
    is_date: bool = False
    is_categorical: bool = False


class DataColumnCreate(DataColumnBase):
    """Schema for creating a data column."""
    position: int = Field(..., gt=0)
    original_name: Optional[str] = Field(None, max_length=255)
    null_count: int = Field(default=0, ge=0)
    unique_count: Optional[int] = Field(None, ge=0)
    min_value: Optional[float] = None
    max_value: Optional[float] = None
    mean_value: Optional[float] = None
    median_value: Optional[float] = None
    std_deviation: Optional[float] = None
    sample_values: Optional[List[Any]] = None
    format_pattern: Optional[str] = Field(None, max_length=100)
    description: Optional[str] = None


class DataColumnUpdate(BaseModel):
    """Schema for updating a data column."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    data_type: Optional[str] = Field(None, regex="^(integer|decimal|text|date|datetime|boolean|json)$")
    description: Optional[str] = None


class DataColumn(DataColumnBase):
    """Data column response schema."""
    id: UUID
    dataset_id: UUID
    position: int
    original_name: Optional[str]
    null_count: int
    unique_count: Optional[int]
    min_value: Optional[float]
    max_value: Optional[float]
    mean_value: Optional[float]
    median_value: Optional[float]
    std_deviation: Optional[float]
    sample_values: Optional[List[Any]]
    format_pattern: Optional[str]
    description: Optional[str]
    created_at: datetime
    
    # Computed properties
    null_percentage: Optional[float]
    unique_percentage: Optional[float]
    
    class Config:
        from_attributes = True


class DatasetBase(BaseModel):
    """Base dataset schema."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    tags: List[str] = Field(default_factory=list)
    is_public: bool = False


class DatasetCreate(DatasetBase):
    """Schema for creating a dataset."""
    pass


class DatasetUpdate(BaseModel):
    """Schema for updating a dataset."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    tags: Optional[List[str]] = None
    is_public: Optional[bool] = None


class DatasetUpload(BaseModel):
    """Schema for dataset upload metadata."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    tags: List[str] = Field(default_factory=list)
    auto_process: bool = True


class Dataset(DatasetBase):
    """Dataset response schema."""
    id: UUID
    organization_id: UUID
    user_id: UUID
    file_name: Optional[str]
    file_path: Optional[str]
    file_size: Optional[int]
    file_type: Optional[str]
    mime_type: Optional[str]
    checksum: Optional[str]
    row_count: Optional[int]
    column_count: Optional[int]
    status: str
    processing_started_at: Optional[datetime]
    processing_completed_at: Optional[datetime]
    error_message: Optional[str]
    metadata: Dict[str, Any]
    data_quality_score: Optional[float]
    created_at: datetime
    updated_at: datetime
    
    # Computed properties
    is_processed: bool
    is_processing: bool
    has_error: bool
    file_size_mb: Optional[float]
    
    class Config:
        from_attributes = True


class DatasetDetailed(Dataset):
    """Detailed dataset response with columns."""
    columns: List[DataColumn] = []


class DatasetList(BaseModel):
    """Dataset list response schema."""
    data: List[Dataset]
    total: int
    page: int
    limit: int
    pages: int
    has_next: bool
    has_prev: bool


class DataValidationRuleBase(BaseModel):
    """Base validation rule schema."""
    rule_type: str = Field(..., regex="^(required|range|format|unique|custom)$")
    rule_config: Dict[str, Any]
    is_active: bool = True


class DataValidationRuleCreate(DataValidationRuleBase):
    """Schema for creating a validation rule."""
    column_id: Optional[UUID] = None


class DataValidationRule(DataValidationRuleBase):
    """Validation rule response schema."""
    id: UUID
    dataset_id: UUID
    column_id: Optional[UUID]
    created_at: datetime
    
    class Config:
        from_attributes = True


class DataProcessingJobBase(BaseModel):
    """Base processing job schema."""
    job_type: str = Field(..., regex="^(parse|validate|clean|transform|analyze)$")


class DataProcessingJobCreate(DataProcessingJobBase):
    """Schema for creating a processing job."""
    pass


class DataProcessingJob(DataProcessingJobBase):
    """Processing job response schema."""
    id: UUID
    dataset_id: UUID
    status: str
    progress_percentage: int
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    error_message: Optional[str]
    result_data: Optional[Dict[str, Any]]
    created_at: datetime
    
    # Computed properties
    is_running: bool
    is_completed: bool
    has_failed: bool
    duration_seconds: Optional[int]
    
    class Config:
        from_attributes = True


class FileUploadResponse(BaseModel):
    """File upload response schema."""
    dataset_id: UUID
    file_name: str
    file_size: int
    file_type: str
    status: str
    message: str
    processing_job_id: Optional[UUID] = None


class DataPreview(BaseModel):
    """Data preview schema."""
    columns: List[str]
    sample_data: List[Dict[str, Any]]
    total_rows: int
    data_types: Dict[str, str]
    null_counts: Dict[str, int]
    summary_stats: Dict[str, Dict[str, Any]]


class DataQualityReport(BaseModel):
    """Data quality report schema."""
    overall_score: float = Field(..., ge=0, le=1)
    total_rows: int
    total_columns: int
    missing_data_percentage: float
    duplicate_rows: int
    data_type_issues: List[Dict[str, Any]]
    outliers_detected: List[Dict[str, Any]]
    recommendations: List[str]
    column_quality: Dict[str, Dict[str, Any]]


class ProcessingProgress(BaseModel):
    """Processing progress schema for real-time updates."""
    job_id: UUID
    dataset_id: UUID
    job_type: str
    status: str
    progress_percentage: int
    current_step: str
    estimated_completion: Optional[datetime]
    error_message: Optional[str]
