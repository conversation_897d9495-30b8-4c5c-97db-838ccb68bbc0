"""
JuliusAI Test Configuration
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool
import os

from app.main import app
from app.database import get_db, Base
from app.config import get_settings

# Test database URL
TEST_DATABASE_URL = "sqlite:///:memory:"

# Create test engine
engine = create_engine(
    TEST_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)

# Create test session factory
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    """Override database dependency for testing."""
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


def override_get_settings():
    """Override settings for testing."""
    settings = get_settings()
    settings.environment = "test"
    settings.debug = True
    settings.secret_key = "test-secret-key-for-testing-only"
    return settings


# Override dependencies
app.dependency_overrides[get_db] = override_get_db


@pytest.fixture(scope="session")
def test_app():
    """Create test FastAPI application."""
    return app


@pytest.fixture(scope="session")
def client(test_app):
    """Create test client."""
    return TestClient(test_app)


@pytest.fixture(scope="function")
def db_session():
    """Create test database session."""
    # Create tables
    Base.metadata.create_all(bind=engine)
    
    # Create session
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.close()
        # Drop tables after test
        Base.metadata.drop_all(bind=engine)


@pytest.fixture(scope="function")
def test_user_data():
    """Test user registration data."""
    return {
        "email": "<EMAIL>",
        "password": "TestPassword123!",
        "first_name": "Test",
        "last_name": "User",
        "role": "analyst",
        "organization_name": "Test Organization",
        "organization_slug": "test-org"
    }


@pytest.fixture(scope="function")
def test_login_data():
    """Test user login data."""
    return {
        "email": "<EMAIL>",
        "password": "TestPassword123!",
        "remember_me": False
    }
