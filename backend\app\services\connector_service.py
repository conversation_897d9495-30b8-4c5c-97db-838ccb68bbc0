"""
JuliusAI Data Connector Service
"""

import pandas as pd
import numpy as np
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from typing import Optional, List, Dict, Any, Tuple
import logging
from datetime import datetime, timedelta
import json
import asyncio
import httpx
from pathlib import Path
import io
import base64
from cryptography.fernet import Fernet

# External API libraries
try:
    import yfinance as yf
    from alpha_vantage.timeseries import TimeSeries
    import quandl
    import boto3
    from google.cloud import storage as gcs
    import dropbox
    import pymongo
    import mysql.connector
    import psycopg2
except ImportError as e:
    logging.warning(f"Some external libraries not available: {e}")

from app.models.connector import (
    DataSource,
    DataSyncJob,
    APICredential,
    DataConnectorTemplate,
    DataTransformation,
    DataSourceMetric,
)
from app.models.data import Dataset
from app.models.user import User
from app.schemas.connector import (
    DataSourceCreate,
    DataSyncJobCreate,
    APICredentialCreate,
    ConnectionTestRequest,
    ConnectionTestResult,
    DataPreview,
    YahooFinanceConfig,
    AlphaVantageConfig,
    DatabaseConfig,
    CloudStorageConfig,
)
from app.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class ConnectorService:
    """Service for handling external data source connections."""

    def __init__(self, db: Session):
        self.db = db
        self.encryption_key = self._get_encryption_key()
        self.fernet = Fernet(self.encryption_key)
        self.temp_dir = Path(settings.upload_dir) / "temp"
        self.temp_dir.mkdir(parents=True, exist_ok=True)

    def _get_encryption_key(self) -> bytes:
        """Get or generate encryption key for credentials."""
        # In production, this should be stored securely (e.g., AWS KMS, HashiCorp Vault)
        key_file = Path(settings.upload_dir) / ".encryption_key"

        if key_file.exists():
            return key_file.read_bytes()
        else:
            key = Fernet.generate_key()
            key_file.write_bytes(key)
            return key

    def _encrypt_credentials(self, credentials: Dict[str, Any]) -> str:
        """Encrypt credential data."""
        try:
            credentials_json = json.dumps(credentials)
            encrypted_data = self.fernet.encrypt(credentials_json.encode())
            return base64.b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error(f"Error encrypting credentials: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to encrypt credentials",
            )

    def _decrypt_credentials(self, encrypted_credentials: str) -> Dict[str, Any]:
        """Decrypt credential data."""
        try:
            encrypted_data = base64.b64decode(encrypted_credentials.encode())
            decrypted_data = self.fernet.decrypt(encrypted_data)
            return json.loads(decrypted_data.decode())
        except Exception as e:
            logger.error(f"Error decrypting credentials: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to decrypt credentials",
            )

    async def create_data_source(
        self, source_data: DataSourceCreate, current_user: User
    ) -> DataSource:
        """
        Create a new data source.

        Args:
            source_data: Data source configuration
            current_user: Current authenticated user

        Returns:
            DataSource: Created data source instance
        """
        try:
            # Encrypt authentication config if provided
            encrypted_auth_config = None
            if source_data.authentication_config:
                encrypted_auth_config = self._encrypt_credentials(
                    source_data.authentication_config
                )

            # Create data source instance
            db_source = DataSource(
                organization_id=current_user.organization_id,
                user_id=current_user.id,
                name=source_data.name,
                description=source_data.description,
                source_type=source_data.source_type,
                provider=source_data.provider,
                connection_config=source_data.connection_config,
                authentication_config=encrypted_auth_config,
                sync_config=source_data.sync_config or {},
                data_mapping=source_data.data_mapping or {},
                transformation_rules=source_data.transformation_rules or {},
                sync_frequency=source_data.sync_frequency,
                sync_schedule=source_data.sync_schedule,
                auto_sync_enabled=source_data.auto_sync_enabled,
                tags=source_data.tags or [],
                status="inactive",
            )

            self.db.add(db_source)
            self.db.commit()
            self.db.refresh(db_source)

            logger.info(f"Data source created: {db_source.id}")
            return db_source

        except Exception as e:
            logger.error(f"Error creating data source: {e}")
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create data source",
            )

    async def test_connection(
        self, test_request: ConnectionTestRequest, current_user: User
    ) -> ConnectionTestResult:
        """
        Test connection to external data source.

        Args:
            test_request: Connection test configuration
            current_user: Current authenticated user

        Returns:
            ConnectionTestResult: Test results
        """
        try:
            start_time = datetime.utcnow()

            if test_request.provider == "yahoo_finance":
                result = await self._test_yahoo_finance_connection(test_request)
            elif test_request.provider == "alpha_vantage":
                result = await self._test_alpha_vantage_connection(test_request)
            elif test_request.provider == "aws_s3":
                result = await self._test_aws_s3_connection(test_request)
            elif test_request.provider == "mysql":
                result = await self._test_mysql_connection(test_request)
            elif test_request.provider == "postgresql":
                result = await self._test_postgresql_connection(test_request)
            else:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Provider {test_request.provider} not supported",
                )

            # Calculate response time
            end_time = datetime.utcnow()
            response_time_ms = int((end_time - start_time).total_seconds() * 1000)
            result.response_time_ms = response_time_ms

            return result

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error testing connection: {e}")
            return ConnectionTestResult(
                success=False,
                message=f"Connection test failed: {str(e)}",
                error_details={"error": str(e), "type": type(e).__name__},
            )

    async def _test_yahoo_finance_connection(
        self, test_request: ConnectionTestRequest
    ) -> ConnectionTestResult:
        """Test Yahoo Finance connection."""
        try:
            config = YahooFinanceConfig(**test_request.connection_config)

            # Test with first symbol
            symbol = config.symbols[0]
            ticker = yf.Ticker(symbol)

            # Get basic info to test connection
            info = ticker.info
            if not info:
                return ConnectionTestResult(
                    success=False, message=f"No data found for symbol {symbol}"
                )

            # Get sample data
            hist = ticker.history(period="5d")
            if hist.empty:
                return ConnectionTestResult(
                    success=False,
                    message=f"No historical data found for symbol {symbol}",
                )

            # Convert to sample data
            sample_data = []
            for date, row in hist.head(5).iterrows():
                sample_data.append(
                    {
                        "date": date.strftime("%Y-%m-%d"),
                        "symbol": symbol,
                        "open": float(row["Open"]),
                        "high": float(row["High"]),
                        "low": float(row["Low"]),
                        "close": float(row["Close"]),
                        "volume": int(row["Volume"]),
                    }
                )

            return ConnectionTestResult(
                success=True,
                message=f"Successfully connected to Yahoo Finance for {symbol}",
                data_sample=sample_data,
                capabilities=["historical_data", "real_time_quotes", "company_info"],
            )

        except Exception as e:
            return ConnectionTestResult(
                success=False,
                message=f"Yahoo Finance connection failed: {str(e)}",
                error_details={"error": str(e)},
            )

    async def _test_alpha_vantage_connection(
        self, test_request: ConnectionTestRequest
    ) -> ConnectionTestResult:
        """Test Alpha Vantage connection."""
        try:
            auth_config = test_request.authentication_config or {}
            api_key = auth_config.get("api_key")

            if not api_key:
                return ConnectionTestResult(
                    success=False, message="API key required for Alpha Vantage"
                )

            config = AlphaVantageConfig(**test_request.connection_config)
            ts = TimeSeries(key=api_key, output_format="pandas")

            # Test with a simple query
            if config.symbol:
                data, meta_data = ts.get_daily(
                    symbol=config.symbol, outputsize="compact"
                )

                if data.empty:
                    return ConnectionTestResult(
                        success=False,
                        message=f"No data found for symbol {config.symbol}",
                    )

                # Convert to sample data
                sample_data = []
                for date, row in data.head(5).iterrows():
                    sample_data.append(
                        {
                            "date": date.strftime("%Y-%m-%d"),
                            "symbol": config.symbol,
                            "open": float(row["1. open"]),
                            "high": float(row["2. high"]),
                            "low": float(row["3. low"]),
                            "close": float(row["4. close"]),
                            "volume": int(row["5. volume"]),
                        }
                    )

                return ConnectionTestResult(
                    success=True,
                    message=f"Successfully connected to Alpha Vantage for {config.symbol}",
                    data_sample=sample_data,
                    capabilities=[
                        "historical_data",
                        "real_time_quotes",
                        "technical_indicators",
                    ],
                )
            else:
                return ConnectionTestResult(
                    success=False, message="Symbol required for Alpha Vantage test"
                )

        except Exception as e:
            return ConnectionTestResult(
                success=False,
                message=f"Alpha Vantage connection failed: {str(e)}",
                error_details={"error": str(e)},
            )

    async def _test_aws_s3_connection(
        self, test_request: ConnectionTestRequest
    ) -> ConnectionTestResult:
        """Test AWS S3 connection."""
        try:
            auth_config = test_request.authentication_config or {}
            config = CloudStorageConfig(**test_request.connection_config)

            # Create S3 client
            s3_client = boto3.client(
                "s3",
                aws_access_key_id=auth_config.get("access_key_id"),
                aws_secret_access_key=auth_config.get("secret_access_key"),
                region_name=config.region or "us-east-1",
            )

            # Test bucket access
            response = s3_client.list_objects_v2(Bucket=config.bucket_name, MaxKeys=5)

            objects = response.get("Contents", [])
            sample_data = []

            for obj in objects[:5]:
                sample_data.append(
                    {
                        "key": obj["Key"],
                        "size": obj["Size"],
                        "last_modified": obj["LastModified"].isoformat(),
                        "storage_class": obj.get("StorageClass", "STANDARD"),
                    }
                )

            return ConnectionTestResult(
                success=True,
                message=f"Successfully connected to S3 bucket {config.bucket_name}",
                data_sample=sample_data,
                capabilities=["file_upload", "file_download", "file_listing"],
            )

        except Exception as e:
            return ConnectionTestResult(
                success=False,
                message=f"AWS S3 connection failed: {str(e)}",
                error_details={"error": str(e)},
            )

    async def _test_mysql_connection(
        self, test_request: ConnectionTestRequest
    ) -> ConnectionTestResult:
        """Test MySQL database connection."""
        try:
            auth_config = test_request.authentication_config or {}
            config = DatabaseConfig(**test_request.connection_config)

            # Create connection
            connection = mysql.connector.connect(
                host=config.host,
                port=config.port,
                database=config.database,
                user=auth_config.get("username"),
                password=auth_config.get("password"),
                connection_timeout=config.connection_timeout,
            )

            cursor = connection.cursor(dictionary=True)

            # Test query
            if config.query:
                cursor.execute(config.query)
                results = cursor.fetchmany(5)
            elif config.table:
                cursor.execute(f"SELECT * FROM {config.table} LIMIT 5")
                results = cursor.fetchall()
            else:
                cursor.execute("SHOW TABLES")
                results = cursor.fetchall()

            cursor.close()
            connection.close()

            return ConnectionTestResult(
                success=True,
                message=f"Successfully connected to MySQL database {config.database}",
                data_sample=results,
                capabilities=["sql_queries", "table_access", "real_time_data"],
            )

        except Exception as e:
            return ConnectionTestResult(
                success=False,
                message=f"MySQL connection failed: {str(e)}",
                error_details={"error": str(e)},
            )

    async def _test_postgresql_connection(
        self, test_request: ConnectionTestRequest
    ) -> ConnectionTestResult:
        """Test PostgreSQL database connection."""
        try:
            auth_config = test_request.authentication_config or {}
            config = DatabaseConfig(**test_request.connection_config)

            # Create connection string
            conn_string = f"host={config.host} port={config.port} dbname={config.database} user={auth_config.get('username')} password={auth_config.get('password')}"

            # Create connection
            connection = psycopg2.connect(conn_string)
            cursor = connection.cursor()

            # Test query
            if config.query:
                cursor.execute(config.query)
                results = cursor.fetchmany(5)
                columns = [desc[0] for desc in cursor.description]
                sample_data = [dict(zip(columns, row)) for row in results]
            elif config.table:
                cursor.execute(f"SELECT * FROM {config.table} LIMIT 5")
                results = cursor.fetchall()
                columns = [desc[0] for desc in cursor.description]
                sample_data = [dict(zip(columns, row)) for row in results]
            else:
                cursor.execute(
                    "SELECT table_name FROM information_schema.tables WHERE table_schema = 'public'"
                )
                results = cursor.fetchall()
                sample_data = [{"table_name": row[0]} for row in results]

            cursor.close()
            connection.close()

            return ConnectionTestResult(
                success=True,
                message=f"Successfully connected to PostgreSQL database {config.database}",
                data_sample=sample_data,
                capabilities=["sql_queries", "table_access", "real_time_data"],
            )

        except Exception as e:
            return ConnectionTestResult(
                success=False,
                message=f"PostgreSQL connection failed: {str(e)}",
                error_details={"error": str(e)},
            )

    async def create_sync_job(
        self, job_data: DataSyncJobCreate, current_user: User
    ) -> DataSyncJob:
        """
        Create and start a data synchronization job.

        Args:
            job_data: Sync job configuration
            current_user: Current authenticated user

        Returns:
            DataSyncJob: Created sync job instance
        """
        try:
            # Verify data source exists and user has access
            data_source = (
                self.db.query(DataSource)
                .filter(
                    DataSource.id == job_data.data_source_id,
                    DataSource.organization_id == current_user.organization_id,
                )
                .first()
            )

            if not data_source:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Data source not found",
                )

            # Create sync job instance
            db_job = DataSyncJob(
                data_source_id=job_data.data_source_id,
                job_type=job_data.job_type,
                sync_parameters=job_data.sync_parameters or {},
                date_range_start=job_data.date_range_start,
                date_range_end=job_data.date_range_end,
                status="pending",
            )

            self.db.add(db_job)
            self.db.commit()
            self.db.refresh(db_job)

            # Start sync job in background
            await self._execute_sync_job(db_job, data_source)

            logger.info(f"Sync job created: {db_job.id}")
            return db_job

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating sync job: {e}")
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create sync job",
            )

    async def _execute_sync_job(self, job: DataSyncJob, data_source: DataSource):
        """Execute a data synchronization job."""
        try:
            job.status = "running"
            job.started_at = datetime.utcnow()
            self.db.commit()

            # Decrypt authentication config
            auth_config = {}
            if data_source.authentication_config:
                auth_config = self._decrypt_credentials(
                    data_source.authentication_config
                )

            # Execute sync based on provider
            if data_source.provider == "yahoo_finance":
                await self._sync_yahoo_finance_data(job, data_source, auth_config)
            elif data_source.provider == "alpha_vantage":
                await self._sync_alpha_vantage_data(job, data_source, auth_config)
            elif data_source.provider == "aws_s3":
                await self._sync_aws_s3_data(job, data_source, auth_config)
            elif data_source.provider in ["mysql", "postgresql"]:
                await self._sync_database_data(job, data_source, auth_config)
            else:
                raise ValueError(
                    f"Provider {data_source.provider} not supported for sync"
                )

            # Update job status
            job.status = "completed"
            job.completed_at = datetime.utcnow()
            job.progress_percentage = 100

            # Update data source last sync info
            data_source.last_sync_at = job.completed_at
            data_source.last_sync_status = "success"
            data_source.last_sync_records = job.records_created + job.records_updated
            data_source.status = "active"

            self.db.commit()

        except Exception as e:
            logger.error(f"Error executing sync job: {e}")
            job.status = "failed"
            job.error_message = str(e)
            job.completed_at = datetime.utcnow()

            data_source.last_sync_status = "failed"
            data_source.last_error_message = str(e)

            self.db.commit()

    async def _sync_yahoo_finance_data(
        self, job: DataSyncJob, data_source: DataSource, auth_config: Dict[str, Any]
    ):
        """Sync data from Yahoo Finance."""
        try:
            config = YahooFinanceConfig(**data_source.connection_config)
            all_data = []

            for i, symbol in enumerate(config.symbols):
                # Update progress
                progress = int(
                    (i / len(config.symbols)) * 90
                )  # Reserve 10% for final processing
                job.progress_percentage = progress
                self.db.commit()

                try:
                    ticker = yf.Ticker(symbol)

                    # Determine date range
                    if job.date_range_start and job.date_range_end:
                        start_date = job.date_range_start.strftime("%Y-%m-%d")
                        end_date = job.date_range_end.strftime("%Y-%m-%d")
                        hist = ticker.history(
                            start=start_date, end=end_date, interval=config.interval
                        )
                    else:
                        hist = ticker.history(
                            period=config.period, interval=config.interval
                        )

                    if not hist.empty:
                        # Convert to records
                        for date, row in hist.iterrows():
                            record = {
                                "date": date.strftime("%Y-%m-%d %H:%M:%S"),
                                "symbol": symbol,
                                "open": float(row["Open"]),
                                "high": float(row["High"]),
                                "low": float(row["Low"]),
                                "close": float(row["Close"]),
                                "volume": int(row["Volume"]),
                                "source": "yahoo_finance",
                                "synced_at": datetime.utcnow().isoformat(),
                            }

                            if config.include_dividends and "Dividends" in row:
                                record["dividends"] = float(row["Dividends"])

                            if config.include_splits and "Stock Splits" in row:
                                record["stock_splits"] = float(row["Stock Splits"])

                            all_data.append(record)

                        job.records_processed += len(hist)
                        job.records_created += len(hist)

                except Exception as symbol_error:
                    logger.warning(f"Error syncing symbol {symbol}: {symbol_error}")
                    job.records_failed += 1

            # Save data to dataset
            if all_data:
                await self._save_sync_data(job, data_source, all_data)

            # Final progress update
            job.progress_percentage = 100
            self.db.commit()

        except Exception as e:
            logger.error(f"Error syncing Yahoo Finance data: {e}")
            raise

    async def _sync_alpha_vantage_data(
        self, job: DataSyncJob, data_source: DataSource, auth_config: Dict[str, Any]
    ):
        """Sync data from Alpha Vantage."""
        try:
            api_key = auth_config.get("api_key")
            if not api_key:
                raise ValueError("API key required for Alpha Vantage")

            config = AlphaVantageConfig(**data_source.connection_config)
            ts = TimeSeries(key=api_key, output_format="pandas")

            all_data = []

            if config.symbol:
                job.progress_percentage = 20
                self.db.commit()

                # Get data based on function
                if config.function == "TIME_SERIES_DAILY":
                    data, meta_data = ts.get_daily(
                        symbol=config.symbol, outputsize=config.outputsize
                    )
                elif config.function == "TIME_SERIES_INTRADAY":
                    data, meta_data = ts.get_intraday(
                        symbol=config.symbol,
                        interval="1min",
                        outputsize=config.outputsize,
                    )
                else:
                    raise ValueError(f"Function {config.function} not supported")

                job.progress_percentage = 70
                self.db.commit()

                if not data.empty:
                    # Convert to records
                    for date, row in data.iterrows():
                        record = {
                            "date": date.strftime("%Y-%m-%d %H:%M:%S"),
                            "symbol": config.symbol,
                            "open": float(row.iloc[0]),
                            "high": float(row.iloc[1]),
                            "low": float(row.iloc[2]),
                            "close": float(row.iloc[3]),
                            "volume": int(row.iloc[4]),
                            "source": "alpha_vantage",
                            "synced_at": datetime.utcnow().isoformat(),
                        }
                        all_data.append(record)

                    job.records_processed = len(data)
                    job.records_created = len(data)

            # Save data to dataset
            if all_data:
                await self._save_sync_data(job, data_source, all_data)

            job.progress_percentage = 100
            self.db.commit()

        except Exception as e:
            logger.error(f"Error syncing Alpha Vantage data: {e}")
            raise

    async def _sync_database_data(
        self, job: DataSyncJob, data_source: DataSource, auth_config: Dict[str, Any]
    ):
        """Sync data from database sources."""
        try:
            config = DatabaseConfig(**data_source.connection_config)

            if data_source.provider == "mysql":
                connection = mysql.connector.connect(
                    host=config.host,
                    port=config.port,
                    database=config.database,
                    user=auth_config.get("username"),
                    password=auth_config.get("password"),
                )
                cursor = connection.cursor(dictionary=True)
            elif data_source.provider == "postgresql":
                conn_string = f"host={config.host} port={config.port} dbname={config.database} user={auth_config.get('username')} password={auth_config.get('password')}"
                connection = psycopg2.connect(conn_string)
                cursor = connection.cursor()

            job.progress_percentage = 20
            self.db.commit()

            # Execute query
            if config.query:
                query = config.query
            elif config.table:
                query = f"SELECT * FROM {config.table}"
                if job.date_range_start and job.date_range_end:
                    # Assume there's a date column for filtering
                    query += f" WHERE date_column >= '{job.date_range_start}' AND date_column <= '{job.date_range_end}'"
            else:
                raise ValueError("Either query or table must be specified")

            cursor.execute(query)

            job.progress_percentage = 50
            self.db.commit()

            # Fetch data in batches
            batch_size = 1000
            all_data = []

            while True:
                rows = cursor.fetchmany(batch_size)
                if not rows:
                    break

                if data_source.provider == "mysql":
                    # MySQL cursor returns dictionaries
                    batch_data = rows
                else:
                    # PostgreSQL cursor returns tuples
                    columns = [desc[0] for desc in cursor.description]
                    batch_data = [dict(zip(columns, row)) for row in rows]

                # Add metadata
                for record in batch_data:
                    record["source"] = data_source.provider
                    record["synced_at"] = datetime.utcnow().isoformat()

                all_data.extend(batch_data)
                job.records_processed += len(batch_data)
                job.records_created += len(batch_data)

                # Update progress
                job.progress_percentage = min(90, 50 + (len(all_data) // 100))
                self.db.commit()

            cursor.close()
            connection.close()

            # Save data to dataset
            if all_data:
                await self._save_sync_data(job, data_source, all_data)

            job.progress_percentage = 100
            self.db.commit()

        except Exception as e:
            logger.error(f"Error syncing database data: {e}")
            raise

    async def _save_sync_data(
        self, job: DataSyncJob, data_source: DataSource, data: List[Dict[str, Any]]
    ):
        """Save synchronized data as a dataset."""
        try:
            # Convert to DataFrame
            df = pd.DataFrame(data)

            # Generate filename
            timestamp = datetime.utcnow().strftime("%Y%m%d_%H%M%S")
            filename = f"{data_source.name}_{timestamp}.csv"
            file_path = self.temp_dir / filename

            # Save to CSV
            df.to_csv(file_path, index=False)

            # Create dataset record
            dataset = Dataset(
                organization_id=data_source.organization_id,
                user_id=data_source.user_id,
                data_source_id=data_source.id,
                name=f"{data_source.name} - {timestamp}",
                description=f"Data synced from {data_source.provider} on {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')}",
                file_path=str(file_path),
                file_type="csv",
                file_size=file_path.stat().st_size,
                status="processed",
                row_count=len(df),
                column_count=len(df.columns),
            )

            self.db.add(dataset)
            self.db.commit()

            # Update job result summary
            job.result_summary = {
                "dataset_id": str(dataset.id),
                "filename": filename,
                "records_count": len(data),
                "columns": list(df.columns),
                "file_size_bytes": dataset.file_size,
            }

            logger.info(f"Sync data saved as dataset: {dataset.id}")

        except Exception as e:
            logger.error(f"Error saving sync data: {e}")
            raise
