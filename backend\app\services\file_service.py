"""
JuliusAI File Upload and Management Service
"""
import os
import hashlib
import mimetypes
from pathlib import Path
from typing import Op<PERSON>, <PERSON>ple, BinaryIO
from fastapi import UploadFile, HTTPException, status
import aiofiles
import logging

from app.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class FileService:
    """Service for handling file uploads and management."""
    
    def __init__(self):
        self.upload_dir = Path(settings.upload_dir)
        self.max_file_size = settings.max_file_size_bytes
        self.allowed_types = settings.allowed_file_types
        
        # Create upload directory if it doesn't exist
        self.upload_dir.mkdir(parents=True, exist_ok=True)
    
    async def validate_file(self, file: UploadFile) -> Tuple[bool, Optional[str]]:
        """
        Validate uploaded file.
        
        Args:
            file: Uploaded file object
            
        Returns:
            Tuple[bool, Optional[str]]: (is_valid, error_message)
        """
        try:
            # Check file size
            if file.size and file.size > self.max_file_size:
                return False, f"File size ({file.size} bytes) exceeds maximum allowed size ({self.max_file_size} bytes)"
            
            # Check file extension
            if file.filename:
                file_extension = Path(file.filename).suffix.lower().lstrip('.')
                if file_extension not in self.allowed_types:
                    return False, f"File type '{file_extension}' not allowed. Allowed types: {', '.join(self.allowed_types)}"
            else:
                return False, "Filename is required"
            
            # Check MIME type
            mime_type, _ = mimetypes.guess_type(file.filename)
            allowed_mime_types = {
                'csv': ['text/csv', 'application/csv', 'text/plain'],
                'xlsx': ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'],
                'xls': ['application/vnd.ms-excel']
            }
            
            file_extension = Path(file.filename).suffix.lower().lstrip('.')
            if file_extension in allowed_mime_types:
                expected_mime_types = allowed_mime_types[file_extension]
                if mime_type and mime_type not in expected_mime_types:
                    logger.warning(f"MIME type mismatch: expected {expected_mime_types}, got {mime_type}")
                    # Don't fail on MIME type mismatch, just log it
            
            return True, None
            
        except Exception as e:
            logger.error(f"File validation error: {e}")
            return False, f"File validation failed: {str(e)}"
    
    async def save_file(self, file: UploadFile, organization_id: str, user_id: str) -> Tuple[str, str, int]:
        """
        Save uploaded file to disk.
        
        Args:
            file: Uploaded file object
            organization_id: Organization ID for file organization
            user_id: User ID for file organization
            
        Returns:
            Tuple[str, str, int]: (file_path, checksum, file_size)
            
        Raises:
            HTTPException: If file save fails
        """
        try:
            # Create organization and user directories
            org_dir = self.upload_dir / organization_id
            user_dir = org_dir / user_id
            user_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate unique filename
            original_name = Path(file.filename)
            timestamp = int(os.path.getmtime(__file__) * 1000000)  # Microsecond timestamp
            unique_filename = f"{timestamp}_{original_name.name}"
            file_path = user_dir / unique_filename
            
            # Save file and calculate checksum
            file_size = 0
            hash_md5 = hashlib.md5()
            
            async with aiofiles.open(file_path, 'wb') as f:
                # Reset file pointer
                await file.seek(0)
                
                # Read and write file in chunks
                while chunk := await file.read(8192):  # 8KB chunks
                    await f.write(chunk)
                    hash_md5.update(chunk)
                    file_size += len(chunk)
            
            checksum = hash_md5.hexdigest()
            
            logger.info(f"File saved: {file_path} (size: {file_size}, checksum: {checksum})")
            
            return str(file_path), checksum, file_size
            
        except Exception as e:
            logger.error(f"File save error: {e}")
            # Clean up partial file if it exists
            if 'file_path' in locals() and file_path.exists():
                try:
                    file_path.unlink()
                except Exception:
                    pass
            
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to save file: {str(e)}"
            )
    
    async def delete_file(self, file_path: str) -> bool:
        """
        Delete file from disk.
        
        Args:
            file_path: Path to file to delete
            
        Returns:
            bool: True if file was deleted successfully
        """
        try:
            path = Path(file_path)
            if path.exists() and path.is_file():
                path.unlink()
                logger.info(f"File deleted: {file_path}")
                return True
            else:
                logger.warning(f"File not found for deletion: {file_path}")
                return False
                
        except Exception as e:
            logger.error(f"File deletion error: {e}")
            return False
    
    def get_file_info(self, file_path: str) -> Optional[dict]:
        """
        Get file information.
        
        Args:
            file_path: Path to file
            
        Returns:
            Optional[dict]: File information or None if file doesn't exist
        """
        try:
            path = Path(file_path)
            if not path.exists():
                return None
            
            stat = path.stat()
            mime_type, _ = mimetypes.guess_type(str(path))
            
            return {
                "path": str(path),
                "name": path.name,
                "size": stat.st_size,
                "mime_type": mime_type,
                "created_at": stat.st_ctime,
                "modified_at": stat.st_mtime,
                "extension": path.suffix.lower().lstrip('.')
            }
            
        except Exception as e:
            logger.error(f"Error getting file info: {e}")
            return None
    
    def calculate_file_checksum(self, file_path: str) -> Optional[str]:
        """
        Calculate MD5 checksum of file.
        
        Args:
            file_path: Path to file
            
        Returns:
            Optional[str]: MD5 checksum or None if error
        """
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(8192), b""):
                    hash_md5.update(chunk)
            return hash_md5.hexdigest()
            
        except Exception as e:
            logger.error(f"Error calculating checksum: {e}")
            return None
    
    def get_storage_usage(self, organization_id: str) -> dict:
        """
        Get storage usage for organization.
        
        Args:
            organization_id: Organization ID
            
        Returns:
            dict: Storage usage information
        """
        try:
            org_dir = self.upload_dir / organization_id
            if not org_dir.exists():
                return {"total_size": 0, "file_count": 0, "size_mb": 0.0}
            
            total_size = 0
            file_count = 0
            
            for file_path in org_dir.rglob("*"):
                if file_path.is_file():
                    total_size += file_path.stat().st_size
                    file_count += 1
            
            size_mb = round(total_size / (1024 * 1024), 2)
            
            return {
                "total_size": total_size,
                "file_count": file_count,
                "size_mb": size_mb
            }
            
        except Exception as e:
            logger.error(f"Error calculating storage usage: {e}")
            return {"total_size": 0, "file_count": 0, "size_mb": 0.0}
    
    async def cleanup_orphaned_files(self, valid_file_paths: list) -> int:
        """
        Clean up orphaned files not referenced in database.
        
        Args:
            valid_file_paths: List of valid file paths from database
            
        Returns:
            int: Number of files cleaned up
        """
        try:
            cleaned_count = 0
            valid_paths = set(valid_file_paths)
            
            for file_path in self.upload_dir.rglob("*"):
                if file_path.is_file() and str(file_path) not in valid_paths:
                    try:
                        file_path.unlink()
                        cleaned_count += 1
                        logger.info(f"Cleaned up orphaned file: {file_path}")
                    except Exception as e:
                        logger.error(f"Error cleaning up file {file_path}: {e}")
            
            return cleaned_count
            
        except Exception as e:
            logger.error(f"Error during cleanup: {e}")
            return 0
