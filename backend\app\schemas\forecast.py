"""
JuliusAI Forecasting Schemas
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from decimal import Decimal
from uuid import UUID
from enum import Enum


class ModelType(str, Enum):
    """Supported forecasting model types."""
    PROPHET = "prophet"
    ARIMA = "arima"
    LSTM = "lstm"
    LINEAR_REGRESSION = "linear_regression"
    EXPONENTIAL_SMOOTHING = "exponential_smoothing"


class ForecastFrequency(str, Enum):
    """Supported forecast frequencies."""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    YEARLY = "yearly"


class ScenarioType(str, Enum):
    """Forecast scenario types."""
    BASE_CASE = "base_case"
    BEST_CASE = "best_case"
    WORST_CASE = "worst_case"
    OPTIMISTIC = "optimistic"
    PESSIMISTIC = "pessimistic"
    REALISTIC = "realistic"
    CUSTOM = "custom"


class ForecastStatus(str, Enum):
    """Forecast execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"


# Base Schemas
class ForecastModelBase(BaseModel):
    """Base forecast model schema."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    model_type: ModelType
    target_column: str = Field(..., min_length=1, max_length=255)
    features: Optional[List[str]] = None
    parameters: Optional[Dict[str, Any]] = None


class ForecastModelCreate(ForecastModelBase):
    """Schema for creating a forecast model."""
    training_data_start: Optional[datetime] = None
    training_data_end: Optional[datetime] = None
    
    @validator('training_data_end')
    def validate_date_range(cls, v, values):
        if v and 'training_data_start' in values and values['training_data_start']:
            if v <= values['training_data_start']:
                raise ValueError('training_data_end must be after training_data_start')
        return v


class ForecastModelUpdate(BaseModel):
    """Schema for updating a forecast model."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None


class ForecastModel(ForecastModelBase):
    """Forecast model response schema."""
    id: UUID
    organization_id: UUID
    user_id: UUID
    training_data_start: Optional[datetime]
    training_data_end: Optional[datetime]
    validation_metrics: Optional[Dict[str, float]]
    status: str
    training_started_at: Optional[datetime]
    training_completed_at: Optional[datetime]
    error_message: Optional[str]
    created_at: datetime
    updated_at: datetime
    is_active: bool
    
    class Config:
        from_attributes = True


# Forecast Schemas
class ForecastBase(BaseModel):
    """Base forecast schema."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    forecast_horizon: int = Field(..., gt=0, le=365)
    forecast_frequency: ForecastFrequency
    scenario_type: ScenarioType = ScenarioType.BASE_CASE
    confidence_level: float = Field(0.95, ge=0.5, le=0.99)


class ForecastCreate(ForecastBase):
    """Schema for creating a forecast."""
    model_id: UUID
    dataset_id: UUID


class ForecastUpdate(BaseModel):
    """Schema for updating a forecast."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None


class ForecastDataPoint(BaseModel):
    """Individual forecast data point."""
    date: datetime
    value: float
    lower_bound: Optional[float] = None
    upper_bound: Optional[float] = None


class ForecastResult(BaseModel):
    """Forecast result data."""
    forecast_data: List[ForecastDataPoint]
    confidence_intervals: Optional[Dict[str, List[float]]] = None
    feature_importance: Optional[Dict[str, float]] = None
    forecast_accuracy: Optional[Dict[str, float]] = None


class Forecast(ForecastBase):
    """Forecast response schema."""
    id: UUID
    model_id: UUID
    dataset_id: UUID
    forecast_data: Optional[List[Dict[str, Any]]]
    confidence_intervals: Optional[Dict[str, Any]]
    feature_importance: Optional[Dict[str, float]]
    forecast_accuracy: Optional[Dict[str, float]]
    status: ForecastStatus
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    error_message: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Backtest Schemas
class BacktestBase(BaseModel):
    """Base backtest schema."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    backtest_start_date: datetime
    backtest_end_date: datetime
    forecast_horizon: int = Field(..., gt=0, le=365)
    cross_validation_folds: int = Field(5, ge=2, le=20)
    
    @validator('backtest_end_date')
    def validate_backtest_dates(cls, v, values):
        if 'backtest_start_date' in values and v <= values['backtest_start_date']:
            raise ValueError('backtest_end_date must be after backtest_start_date')
        return v


class BacktestCreate(BacktestBase):
    """Schema for creating a backtest."""
    model_id: UUID
    dataset_id: UUID


class BacktestMetrics(BaseModel):
    """Backtest performance metrics."""
    mape: Optional[float] = None
    rmse: Optional[float] = None
    mae: Optional[float] = None
    mse: Optional[float] = None
    r_squared: Optional[float] = None
    directional_accuracy: Optional[float] = None


class BacktestResult(BacktestBase):
    """Backtest result response schema."""
    id: UUID
    model_id: UUID
    dataset_id: UUID
    mape: Optional[Decimal]
    rmse: Optional[Decimal]
    mae: Optional[Decimal]
    mse: Optional[Decimal]
    r_squared: Optional[Decimal]
    directional_accuracy: Optional[Decimal]
    predictions_vs_actual: Optional[Dict[str, Any]]
    residuals_analysis: Optional[Dict[str, Any]]
    performance_by_period: Optional[Dict[str, Any]]
    feature_stability: Optional[Dict[str, Any]]
    status: ForecastStatus
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    execution_time_seconds: Optional[int]
    error_message: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Scenario Schemas
class ForecastScenarioBase(BaseModel):
    """Base forecast scenario schema."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    scenario_type: ScenarioType
    growth_assumptions: Optional[Dict[str, float]] = None
    external_factors: Optional[Dict[str, Any]] = None
    seasonal_adjustments: Optional[Dict[str, float]] = None
    risk_adjustments: Optional[Dict[str, float]] = None
    probability_weight: float = Field(0.33, ge=0.0, le=1.0)
    confidence_level: float = Field(0.80, ge=0.0, le=1.0)


class ForecastScenarioCreate(ForecastScenarioBase):
    """Schema for creating a forecast scenario."""
    review_date: Optional[datetime] = None


class ForecastScenarioUpdate(BaseModel):
    """Schema for updating a forecast scenario."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    growth_assumptions: Optional[Dict[str, float]] = None
    external_factors: Optional[Dict[str, Any]] = None
    seasonal_adjustments: Optional[Dict[str, float]] = None
    risk_adjustments: Optional[Dict[str, float]] = None
    probability_weight: Optional[float] = Field(None, ge=0.0, le=1.0)
    confidence_level: Optional[float] = Field(None, ge=0.0, le=1.0)
    review_date: Optional[datetime] = None
    is_active: Optional[bool] = None


class ForecastScenario(ForecastScenarioBase):
    """Forecast scenario response schema."""
    id: UUID
    organization_id: UUID
    user_id: UUID
    review_date: Optional[datetime]
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Alert Schemas
class ForecastAlertBase(BaseModel):
    """Base forecast alert schema."""
    alert_type: str = Field(..., regex="^(accuracy_drop|significant_change|anomaly)$")
    severity: str = Field("medium", regex="^(low|medium|high|critical)$")
    title: str = Field(..., min_length=1, max_length=255)
    message: str = Field(..., min_length=1)
    threshold_value: Optional[float] = None
    actual_value: Optional[float] = None
    deviation_percentage: Optional[float] = None
    alert_data: Optional[Dict[str, Any]] = None


class ForecastAlert(ForecastAlertBase):
    """Forecast alert response schema."""
    id: UUID
    forecast_id: UUID
    is_acknowledged: bool
    acknowledged_by: Optional[UUID]
    acknowledged_at: Optional[datetime]
    created_at: datetime
    
    class Config:
        from_attributes = True


# List Schemas
class ForecastModelList(BaseModel):
    """Paginated forecast model list."""
    models: List[ForecastModel]
    total: int
    page: int
    size: int
    pages: int


class ForecastList(BaseModel):
    """Paginated forecast list."""
    forecasts: List[Forecast]
    total: int
    page: int
    size: int
    pages: int


class BacktestResultList(BaseModel):
    """Paginated backtest result list."""
    results: List[BacktestResult]
    total: int
    page: int
    size: int
    pages: int
