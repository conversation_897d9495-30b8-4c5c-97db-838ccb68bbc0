"""
JuliusAI Enhanced Security Schemas
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from uuid import UUID
from enum import Enum


class EventCategory(str, Enum):
    """Security event categories."""
    AUTHENTICATION = "authentication"
    AUTHORIZATION = "authorization"
    DATA = "data"
    SYSTEM = "system"
    ADMIN = "admin"


class EventSeverity(str, Enum):
    """Security event severity levels."""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class MFAMethodType(str, Enum):
    """Multi-factor authentication method types."""
    TOTP = "totp"
    SMS = "sms"
    EMAIL = "email"
    BACKUP_CODES = "backup_codes"


class RequestStatus(str, Enum):
    """Access request status."""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    EXPIRED = "expired"


class UrgencyLevel(str, Enum):
    """Request urgency levels."""
    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"
    CRITICAL = "critical"


# Role Schemas
class RoleBase(BaseModel):
    """Base role schema."""
    name: str = Field(..., min_length=1, max_length=100)
    display_name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    is_active: bool = True
    priority: int = Field(0, ge=0, le=100)


class RoleCreate(RoleBase):
    """Schema for creating a role."""
    permissions: Optional[List[UUID]] = Field(default_factory=list)


class RoleUpdate(BaseModel):
    """Schema for updating a role."""
    display_name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    is_active: Optional[bool] = None
    priority: Optional[int] = Field(None, ge=0, le=100)
    permissions: Optional[List[UUID]] = None


class Role(RoleBase):
    """Role response schema."""
    id: UUID
    organization_id: UUID
    is_system_role: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Permission Schemas
class PermissionBase(BaseModel):
    """Base permission schema."""
    name: str = Field(..., min_length=1, max_length=100)
    display_name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    category: str = Field(..., min_length=1, max_length=100)
    resource_type: Optional[str] = None
    action: str = Field(..., min_length=1, max_length=50)
    scope: str = Field("organization", regex="^(organization|own|assigned)$")


class PermissionCreate(PermissionBase):
    """Schema for creating a permission."""
    pass


class Permission(PermissionBase):
    """Permission response schema."""
    id: UUID
    is_system_permission: bool
    is_active: bool
    created_at: datetime
    
    class Config:
        from_attributes = True


# Role Permission Schemas
class RolePermissionBase(BaseModel):
    """Base role permission schema."""
    role_id: UUID
    permission_id: UUID
    granted: bool = True
    conditions: Optional[Dict[str, Any]] = None


class RolePermissionCreate(RolePermissionBase):
    """Schema for creating a role permission."""
    pass


class RolePermission(RolePermissionBase):
    """Role permission response schema."""
    id: UUID
    granted_by: Optional[UUID]
    granted_at: datetime
    
    class Config:
        from_attributes = True


# User Role Schemas
class UserRoleBase(BaseModel):
    """Base user role schema."""
    user_id: UUID
    role_id: UUID
    valid_from: Optional[datetime] = None
    valid_until: Optional[datetime] = None
    conditions: Optional[Dict[str, Any]] = None
    reason: Optional[str] = None


class UserRoleCreate(UserRoleBase):
    """Schema for creating a user role assignment."""
    pass


class UserRoleUpdate(BaseModel):
    """Schema for updating a user role assignment."""
    valid_until: Optional[datetime] = None
    conditions: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    reason: Optional[str] = None


class UserRole(UserRoleBase):
    """User role response schema."""
    id: UUID
    is_active: bool
    assigned_by: Optional[UUID]
    assigned_at: datetime
    is_valid: bool
    
    class Config:
        from_attributes = True


# MFA Schemas
class MFAMethodBase(BaseModel):
    """Base MFA method schema."""
    method_type: MFAMethodType
    phone_number: Optional[str] = Field(None, regex=r"^\+?[1-9]\d{1,14}$")


class MFAMethodCreate(MFAMethodBase):
    """Schema for creating an MFA method."""
    pass


class MFAMethodUpdate(BaseModel):
    """Schema for updating an MFA method."""
    phone_number: Optional[str] = Field(None, regex=r"^\+?[1-9]\d{1,14}$")
    is_active: Optional[bool] = None


class MFAMethod(MFAMethodBase):
    """MFA method response schema."""
    id: UUID
    user_id: UUID
    is_active: bool
    is_verified: bool
    last_used_at: Optional[datetime]
    usage_count: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class MFASetupResponse(BaseModel):
    """MFA setup response with QR code data."""
    method_id: UUID
    qr_code_url: str
    backup_codes: List[str]
    secret_key: str  # For manual entry


class MFAVerificationRequest(BaseModel):
    """MFA verification request."""
    method_id: UUID
    code: str = Field(..., min_length=6, max_length=8)


class MFAVerificationResponse(BaseModel):
    """MFA verification response."""
    verified: bool
    remaining_attempts: Optional[int] = None
    backup_codes_remaining: Optional[int] = None


# Security Event Schemas
class SecurityEventBase(BaseModel):
    """Base security event schema."""
    event_type: str = Field(..., min_length=1, max_length=100)
    event_category: EventCategory
    severity: EventSeverity = EventSeverity.INFO
    event_description: str = Field(..., min_length=1)
    resource_type: Optional[str] = None
    resource_id: Optional[UUID] = None
    action: Optional[str] = None
    additional_data: Optional[Dict[str, Any]] = None


class SecurityEventCreate(SecurityEventBase):
    """Schema for creating a security event."""
    ip_address: Optional[str] = None
    user_agent: Optional[str] = None
    request_id: Optional[str] = None


class SecurityEvent(SecurityEventBase):
    """Security event response schema."""
    id: UUID
    organization_id: UUID
    user_id: Optional[UUID]
    session_id: Optional[UUID]
    ip_address: Optional[str]
    user_agent: Optional[str]
    request_id: Optional[str]
    risk_score: int
    anomaly_indicators: Optional[Dict[str, Any]]
    timestamp: datetime
    processed_at: Optional[datetime]
    
    class Config:
        from_attributes = True


# Login Attempt Schemas
class LoginAttemptBase(BaseModel):
    """Base login attempt schema."""
    email: str
    ip_address: str
    user_agent: Optional[str] = None
    success: bool
    failure_reason: Optional[str] = None
    mfa_required: bool = False
    mfa_completed: bool = False


class LoginAttemptCreate(LoginAttemptBase):
    """Schema for creating a login attempt record."""
    country: Optional[str] = None
    city: Optional[str] = None
    timezone: Optional[str] = None
    risk_factors: Optional[Dict[str, Any]] = None


class LoginAttempt(LoginAttemptBase):
    """Login attempt response schema."""
    id: UUID
    country: Optional[str]
    city: Optional[str]
    timezone: Optional[str]
    risk_score: int
    risk_factors: Optional[Dict[str, Any]]
    attempted_at: datetime
    session_id: Optional[UUID]
    
    class Config:
        from_attributes = True


# Security Policy Schemas
class SecurityPolicyBase(BaseModel):
    """Base security policy schema."""
    policy_type: str = Field(..., min_length=1, max_length=100)
    policy_name: str = Field(..., min_length=1, max_length=255)
    policy_description: Optional[str] = None
    policy_config: Dict[str, Any]
    is_active: bool = True
    is_enforced: bool = True
    enforcement_level: str = Field("strict", regex="^(strict|moderate|advisory)$")


class SecurityPolicyCreate(SecurityPolicyBase):
    """Schema for creating a security policy."""
    pass


class SecurityPolicyUpdate(BaseModel):
    """Schema for updating a security policy."""
    policy_name: Optional[str] = Field(None, min_length=1, max_length=255)
    policy_description: Optional[str] = None
    policy_config: Optional[Dict[str, Any]] = None
    is_active: Optional[bool] = None
    is_enforced: Optional[bool] = None
    enforcement_level: Optional[str] = Field(None, regex="^(strict|moderate|advisory)$")


class SecurityPolicy(SecurityPolicyBase):
    """Security policy response schema."""
    id: UUID
    organization_id: UUID
    created_by: Optional[UUID]
    created_at: datetime
    updated_at: datetime
    last_reviewed_at: Optional[datetime]
    
    class Config:
        from_attributes = True


# Access Request Schemas
class AccessRequestBase(BaseModel):
    """Base access request schema."""
    request_type: str = Field(..., min_length=1, max_length=100)
    resource_type: Optional[str] = None
    resource_id: Optional[UUID] = None
    requested_permissions: Optional[Dict[str, Any]] = None
    business_justification: str = Field(..., min_length=10)
    urgency_level: UrgencyLevel = UrgencyLevel.NORMAL
    access_duration: str = Field("temporary", regex="^(temporary|permanent|specific_date)$")
    valid_until: Optional[datetime] = None


class AccessRequestCreate(AccessRequestBase):
    """Schema for creating an access request."""
    
    @validator("valid_until")
    def validate_valid_until(cls, v, values):
        """Validate valid_until is required for temporary access."""
        if values.get("access_duration") == "specific_date" and not v:
            raise ValueError("valid_until is required for specific_date access duration")
        return v


class AccessRequestUpdate(BaseModel):
    """Schema for updating an access request."""
    status: Optional[RequestStatus] = None
    approval_notes: Optional[str] = None


class AccessRequest(AccessRequestBase):
    """Access request response schema."""
    id: UUID
    organization_id: UUID
    requester_id: UUID
    status: RequestStatus
    approver_id: Optional[UUID]
    approval_notes: Optional[str]
    approved_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# List Schemas
class RoleList(BaseModel):
    """Paginated role list."""
    roles: List[Role]
    total: int
    page: int
    size: int
    pages: int


class PermissionList(BaseModel):
    """Paginated permission list."""
    permissions: List[Permission]
    total: int
    page: int
    size: int
    pages: int


class SecurityEventList(BaseModel):
    """Paginated security event list."""
    events: List[SecurityEvent]
    total: int
    page: int
    size: int
    pages: int


class LoginAttemptList(BaseModel):
    """Paginated login attempt list."""
    attempts: List[LoginAttempt]
    total: int
    page: int
    size: int
    pages: int


class AccessRequestList(BaseModel):
    """Paginated access request list."""
    requests: List[AccessRequest]
    total: int
    page: int
    size: int
    pages: int


# Security Dashboard Schemas
class SecurityDashboard(BaseModel):
    """Security dashboard summary."""
    total_users: int
    active_sessions: int
    failed_logins_24h: int
    security_events_24h: int
    pending_access_requests: int
    high_risk_events: int
    mfa_enabled_users: int
    mfa_adoption_rate: float


class RiskAssessment(BaseModel):
    """Risk assessment summary."""
    overall_risk_score: int
    risk_factors: List[Dict[str, Any]]
    recommendations: List[str]
    last_assessment: datetime
