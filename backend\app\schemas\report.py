"""
JuliusAI Report and Analysis Schemas
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from decimal import Decimal
from uuid import UUID
from enum import Enum


class AnalysisType(str, Enum):
    """Supported analysis types."""
    DESCRIPTIVE = "descriptive"
    TREND = "trend"
    FORECAST = "forecast"
    ANOMALY = "anomaly"
    CORRELATION = "correlation"
    CUSTOM = "custom"


class AnalysisStatus(str, Enum):
    """Analysis execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ReportType(str, Enum):
    """Report types."""
    ANALYSIS = "analysis"
    FORECAST = "forecast"
    SUMMARY = "summary"
    CUSTOM = "custom"


class TemplateType(str, Enum):
    """Report template types."""
    EXECUTIVE = "executive"
    TECHNICAL = "technical"
    PRESENTATION = "presentation"
    DASHBOARD = "dashboard"


class AudienceType(str, Enum):
    """Target audience types."""
    EXECUTIVE = "executive"
    ANALYST = "analyst"
    MANAGER = "manager"
    STAKEHOLDER = "stakeholder"


class ExportFormat(str, Enum):
    """Export format options."""
    JSON = "json"
    PDF = "pdf"
    DOCX = "docx"
    PPTX = "pptx"
    HTML = "html"


# Analysis Schemas
class AnalysisBase(BaseModel):
    """Base analysis schema."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    analysis_type: AnalysisType
    parameters: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None


class AnalysisCreate(AnalysisBase):
    """Schema for creating an analysis."""
    dataset_id: UUID


class AnalysisUpdate(BaseModel):
    """Schema for updating an analysis."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None
    tags: Optional[List[str]] = None
    is_favorite: Optional[bool] = None


class AnalysisMetricBase(BaseModel):
    """Base analysis metric schema."""
    metric_name: str = Field(..., min_length=1, max_length=100)
    metric_value: Optional[Decimal] = None
    metric_unit: Optional[str] = None
    metric_category: Optional[str] = None
    calculation_method: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None


class AnalysisMetric(AnalysisMetricBase):
    """Analysis metric response schema."""
    id: UUID
    analysis_id: UUID
    created_at: datetime
    
    class Config:
        from_attributes = True


class Analysis(AnalysisBase):
    """Analysis response schema."""
    id: UUID
    organization_id: UUID
    dataset_id: UUID
    user_id: UUID
    status: AnalysisStatus
    progress_percentage: int
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    error_message: Optional[str]
    results: Optional[Dict[str, Any]]
    insights: Optional[Dict[str, Any]]
    confidence_score: Optional[Decimal]
    execution_time_ms: Optional[int]
    is_favorite: bool
    created_at: datetime
    updated_at: datetime
    metrics: Optional[List[AnalysisMetric]] = None
    
    class Config:
        from_attributes = True


# Report Template Schemas
class ReportTemplateBase(BaseModel):
    """Base report template schema."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    template_type: TemplateType
    audience: AudienceType
    category: Optional[str] = None
    template_config: Dict[str, Any]
    sections: List[Dict[str, Any]]
    styling: Optional[Dict[str, Any]] = None
    default_parameters: Optional[Dict[str, Any]] = None


class ReportTemplateCreate(ReportTemplateBase):
    """Schema for creating a report template."""
    is_public: bool = False


class ReportTemplateUpdate(BaseModel):
    """Schema for updating a report template."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    template_config: Optional[Dict[str, Any]] = None
    sections: Optional[List[Dict[str, Any]]] = None
    styling: Optional[Dict[str, Any]] = None
    default_parameters: Optional[Dict[str, Any]] = None
    is_public: Optional[bool] = None
    is_active: Optional[bool] = None


class ReportTemplate(ReportTemplateBase):
    """Report template response schema."""
    id: UUID
    organization_id: UUID
    user_id: UUID
    is_public: bool
    is_system_template: bool
    usage_count: int
    last_used_at: Optional[datetime]
    version: str
    created_at: datetime
    updated_at: datetime
    is_active: bool
    
    class Config:
        from_attributes = True


# Report Schemas
class ReportBase(BaseModel):
    """Base report schema."""
    title: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    report_type: ReportType
    audience: AudienceType


class ReportCreate(ReportBase):
    """Schema for creating a report."""
    analysis_id: Optional[UUID] = None
    template_id: Optional[UUID] = None
    generation_parameters: Optional[Dict[str, Any]] = None


class ReportUpdate(BaseModel):
    """Schema for updating a report."""
    title: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    content: Optional[Dict[str, Any]] = None
    executive_summary: Optional[str] = None
    key_insights: Optional[List[Dict[str, Any]]] = None
    recommendations: Optional[List[Dict[str, Any]]] = None


class ReportGenerate(BaseModel):
    """Schema for generating a report."""
    analysis_id: Optional[UUID] = None
    template_id: Optional[UUID] = None
    title: str = Field(..., min_length=1, max_length=255)
    report_type: ReportType
    audience: AudienceType
    include_executive_summary: bool = True
    include_recommendations: bool = True
    custom_sections: Optional[List[Dict[str, Any]]] = None
    generation_parameters: Optional[Dict[str, Any]] = None


class Report(ReportBase):
    """Report response schema."""
    id: UUID
    organization_id: UUID
    analysis_id: Optional[UUID]
    template_id: Optional[UUID]
    user_id: UUID
    content: Dict[str, Any]
    executive_summary: Optional[str]
    key_insights: Optional[List[Dict[str, Any]]]
    recommendations: Optional[List[Dict[str, Any]]]
    format: str
    file_path: Optional[str]
    file_size: Optional[int]
    status: str
    is_shared: bool
    share_token: Optional[str]
    share_expires_at: Optional[datetime]
    generation_parameters: Optional[Dict[str, Any]]
    generation_time_ms: Optional[int]
    ai_model_used: Optional[str]
    generated_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Report Section Schemas
class ReportSectionBase(BaseModel):
    """Base report section schema."""
    section_type: str = Field(..., min_length=1, max_length=50)
    title: str = Field(..., min_length=1, max_length=255)
    content: Dict[str, Any]
    order_index: int = Field(..., ge=0)
    styling: Optional[Dict[str, Any]] = None
    parameters: Optional[Dict[str, Any]] = None
    data_source: Optional[str] = None


class ReportSectionCreate(ReportSectionBase):
    """Schema for creating a report section."""
    pass


class ReportSection(ReportSectionBase):
    """Report section response schema."""
    id: UUID
    report_id: UUID
    ai_generated: bool
    ai_confidence: Optional[Decimal]
    human_reviewed: bool
    reviewed_by: Optional[UUID]
    reviewed_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Export Schemas
class ReportExportRequest(BaseModel):
    """Schema for requesting report export."""
    export_format: ExportFormat
    export_parameters: Optional[Dict[str, Any]] = None


class ReportExport(BaseModel):
    """Report export response schema."""
    id: UUID
    report_id: UUID
    user_id: UUID
    export_format: str
    file_path: str
    file_size: Optional[int]
    export_parameters: Optional[Dict[str, Any]]
    status: str
    error_message: Optional[str]
    download_count: int
    last_downloaded_at: Optional[datetime]
    expires_at: Optional[datetime]
    created_at: datetime
    
    class Config:
        from_attributes = True


# Insight Schemas
class InsightBase(BaseModel):
    """Base insight schema."""
    insight_type: str
    title: str
    content: str
    confidence: float = Field(..., ge=0.0, le=1.0)
    priority: int = Field(1, ge=1, le=10)
    metadata: Optional[Dict[str, Any]] = None


class GeneratedInsight(InsightBase):
    """Generated insight with additional context."""
    supporting_data: Optional[Dict[str, Any]] = None
    recommendations: Optional[List[str]] = None
    impact_level: Optional[str] = None  # low, medium, high, critical


# List Schemas
class AnalysisList(BaseModel):
    """Paginated analysis list."""
    analyses: List[Analysis]
    total: int
    page: int
    size: int
    pages: int


class ReportTemplateList(BaseModel):
    """Paginated report template list."""
    templates: List[ReportTemplate]
    total: int
    page: int
    size: int
    pages: int


class ReportList(BaseModel):
    """Paginated report list."""
    reports: List[Report]
    total: int
    page: int
    size: int
    pages: int


# Summary Schemas
class DataSummary(BaseModel):
    """Data summary for reports."""
    total_rows: int
    total_columns: int
    date_range: Optional[Dict[str, str]] = None
    key_metrics: Dict[str, Any]
    data_quality_score: Optional[float] = None


class AnalysisSummary(BaseModel):
    """Analysis summary for reports."""
    analysis_type: str
    key_findings: List[str]
    confidence_score: Optional[float]
    execution_time: Optional[str]
    metrics_calculated: int


class ReportSummary(BaseModel):
    """Report summary information."""
    id: UUID
    title: str
    report_type: str
    audience: str
    status: str
    created_at: datetime
    file_size: Optional[int]
    download_count: Optional[int] = 0
