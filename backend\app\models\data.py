"""
JuliusAI Data Management Models
"""
from sqlalchemy import Column, String, Boolean, DateTime, Integer, ForeignKey, Text, BigInteger, DECIMAL
from sqlalchemy.dialects.postgresql import UUID, JSONB, ARRAY
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
import uuid
from datetime import datetime
from typing import Optional, List, Dict, Any

from app.database import Base


class Dataset(Base):
    """Dataset model for uploaded data files."""
    
    __tablename__ = "datasets"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    file_name = Column(String(255))
    file_path = Column(String(500))
    file_size = Column(BigInteger)
    file_type = Column(String(50))
    mime_type = Column(String(100))
    checksum = Column(String(64))
    row_count = Column(Integer)
    column_count = Column(Integer)
    status = Column(String(50), default="uploaded", nullable=False)
    processing_started_at = Column(DateTime(timezone=True))
    processing_completed_at = Column(DateTime(timezone=True))
    error_message = Column(Text)
    metadata = Column(JSONB, default=dict)
    data_quality_score = Column(DECIMAL(3, 2))
    tags = Column(ARRAY(String), default=list)
    is_public = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    organization = relationship("Organization", back_populates="datasets")
    user = relationship("User", back_populates="datasets")
    columns = relationship("DataColumn", back_populates="dataset", cascade="all, delete-orphan")
    analyses = relationship("Analysis", back_populates="dataset", cascade="all, delete-orphan")
    processing_jobs = relationship("DataProcessingJob", back_populates="dataset", cascade="all, delete-orphan")
    
    @property
    def is_processed(self) -> bool:
        """Check if dataset is processed."""
        return self.status == "processed"
    
    @property
    def is_processing(self) -> bool:
        """Check if dataset is currently processing."""
        return self.status == "processing"
    
    @property
    def has_error(self) -> bool:
        """Check if dataset has processing errors."""
        return self.status == "failed"
    
    @property
    def file_size_mb(self) -> Optional[float]:
        """Get file size in MB."""
        if self.file_size:
            return round(self.file_size / (1024 * 1024), 2)
        return None
    
    def __repr__(self):
        return f"<Dataset(id={self.id}, name='{self.name}', status='{self.status}')>"


class DataColumn(Base):
    """Data column model for dataset schema information."""
    
    __tablename__ = "data_columns"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    dataset_id = Column(UUID(as_uuid=True), ForeignKey("datasets.id"), nullable=False)
    name = Column(String(255), nullable=False)
    original_name = Column(String(255))
    position = Column(Integer, nullable=False)
    data_type = Column(String(50), nullable=False)
    is_numeric = Column(Boolean, default=False, nullable=False)
    is_date = Column(Boolean, default=False, nullable=False)
    is_categorical = Column(Boolean, default=False, nullable=False)
    null_count = Column(Integer, default=0)
    unique_count = Column(Integer)
    min_value = Column(DECIMAL)
    max_value = Column(DECIMAL)
    mean_value = Column(DECIMAL)
    median_value = Column(DECIMAL)
    std_deviation = Column(DECIMAL)
    sample_values = Column(JSONB)
    format_pattern = Column(String(100))
    description = Column(Text)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    dataset = relationship("Dataset", back_populates="columns")
    validation_rules = relationship("DataValidationRule", back_populates="column", cascade="all, delete-orphan")
    
    @property
    def null_percentage(self) -> Optional[float]:
        """Calculate null percentage."""
        if self.dataset and self.dataset.row_count and self.dataset.row_count > 0:
            return round((self.null_count / self.dataset.row_count) * 100, 2)
        return None
    
    @property
    def unique_percentage(self) -> Optional[float]:
        """Calculate unique percentage."""
        if self.dataset and self.dataset.row_count and self.dataset.row_count > 0 and self.unique_count:
            return round((self.unique_count / self.dataset.row_count) * 100, 2)
        return None
    
    def __repr__(self):
        return f"<DataColumn(id={self.id}, name='{self.name}', type='{self.data_type}')>"


class DataValidationRule(Base):
    """Data validation rules for columns."""
    
    __tablename__ = "data_validation_rules"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    dataset_id = Column(UUID(as_uuid=True), ForeignKey("datasets.id"), nullable=False)
    column_id = Column(UUID(as_uuid=True), ForeignKey("data_columns.id"))
    rule_type = Column(String(50), nullable=False)
    rule_config = Column(JSONB, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    dataset = relationship("Dataset")
    column = relationship("DataColumn", back_populates="validation_rules")
    
    def __repr__(self):
        return f"<DataValidationRule(id={self.id}, type='{self.rule_type}')>"


class DataProcessingJob(Base):
    """Data processing job tracking."""
    
    __tablename__ = "data_processing_jobs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    dataset_id = Column(UUID(as_uuid=True), ForeignKey("datasets.id"), nullable=False)
    job_type = Column(String(50), nullable=False)
    status = Column(String(50), default="pending", nullable=False)
    progress_percentage = Column(Integer, default=0)
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    error_message = Column(Text)
    result_data = Column(JSONB)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    dataset = relationship("Dataset", back_populates="processing_jobs")
    
    @property
    def is_running(self) -> bool:
        """Check if job is currently running."""
        return self.status == "running"
    
    @property
    def is_completed(self) -> bool:
        """Check if job is completed."""
        return self.status == "completed"
    
    @property
    def has_failed(self) -> bool:
        """Check if job has failed."""
        return self.status == "failed"
    
    @property
    def duration_seconds(self) -> Optional[int]:
        """Calculate job duration in seconds."""
        if self.started_at and self.completed_at:
            return int((self.completed_at - self.started_at).total_seconds())
        return None
    
    def __repr__(self):
        return f"<DataProcessingJob(id={self.id}, type='{self.job_type}', status='{self.status}')>"
