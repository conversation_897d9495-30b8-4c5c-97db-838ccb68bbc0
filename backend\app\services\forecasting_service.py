"""
JuliusAI Forecasting Service
"""

import pandas as pd
import numpy as np
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from typing import Optional, List, Dict, Any, Tuple
import logging
from datetime import datetime, timedelta
import pickle
import os
from pathlib import Path
import json

# ML Libraries
from prophet import Prophet
from statsmodels.tsa.arima.model import ARIMA
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_absolute_error, mean_squared_error, r2_score
from sklearn.preprocessing import StandardScaler
import warnings

warnings.filterwarnings("ignore")

from app.models.forecast import (
    ForecastModel,
    Forecast,
    BacktestResult,
    ForecastScenario,
)
from app.models.data import Dataset
from app.models.user import User
from app.schemas.forecast import (
    ForecastModelCreate,
    ForecastCreate,
    BacktestCreate,
    ForecastDataPoint,
    ForecastResult,
    BacktestMetrics,
)
from app.services.data_service import DataService
from app.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class ForecastingService:
    """Service for handling forecasting operations."""

    def __init__(self, db: Session):
        self.db = db
        self.data_service = DataService(db)
        self.models_dir = Path(settings.upload_dir) / "models"
        self.models_dir.mkdir(parents=True, exist_ok=True)

    async def create_forecast_model(
        self, model_data: ForecastModelCreate, current_user: User
    ) -> ForecastModel:
        """
        Create a new forecast model.

        Args:
            model_data: Model configuration data
            current_user: Current authenticated user

        Returns:
            ForecastModel: Created model instance
        """
        try:
            # Create model instance
            db_model = ForecastModel(
                organization_id=current_user.organization_id,
                user_id=current_user.id,
                name=model_data.name,
                description=model_data.description,
                model_type=model_data.model_type,
                target_column=model_data.target_column,
                features=model_data.features or [],
                parameters=model_data.parameters or {},
                training_data_start=model_data.training_data_start,
                training_data_end=model_data.training_data_end,
                status="created",
            )

            self.db.add(db_model)
            self.db.commit()
            self.db.refresh(db_model)

            logger.info(f"Forecast model created: {db_model.id}")
            return db_model

        except Exception as e:
            logger.error(f"Error creating forecast model: {e}")
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create forecast model",
            )

    async def train_model(
        self, model_id: str, dataset_id: str, current_user: User
    ) -> ForecastModel:
        """
        Train a forecast model with the specified dataset.

        Args:
            model_id: Model ID to train
            dataset_id: Dataset ID for training
            current_user: Current authenticated user

        Returns:
            ForecastModel: Updated model with training results
        """
        try:
            # Get model and dataset
            model = (
                self.db.query(ForecastModel)
                .filter(
                    ForecastModel.id == model_id,
                    ForecastModel.organization_id == current_user.organization_id,
                )
                .first()
            )

            if not model:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Forecast model not found",
                )

            dataset = (
                self.db.query(Dataset)
                .filter(
                    Dataset.id == dataset_id,
                    Dataset.organization_id == current_user.organization_id,
                )
                .first()
            )

            if not dataset:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Dataset not found"
                )

            # Update model status
            model.status = "training"
            model.training_started_at = datetime.utcnow()
            self.db.commit()

            # Load and prepare data
            df = await self._load_dataset(dataset)
            prepared_data = self._prepare_training_data(df, model)

            # Train model based on type
            trained_model, metrics = await self._train_model_by_type(
                model, prepared_data
            )

            # Save trained model
            model_path = await self._save_model(trained_model, model.id)

            # Update model with results
            model.status = "trained"
            model.training_completed_at = datetime.utcnow()
            model.model_file_path = str(model_path)
            model.validation_metrics = metrics

            self.db.commit()
            self.db.refresh(model)

            logger.info(f"Model trained successfully: {model.id}")
            return model

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error training model: {e}")
            model.status = "failed"
            model.error_message = str(e)
            self.db.commit()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to train model",
            )

    async def create_forecast(
        self, forecast_data: ForecastCreate, current_user: User
    ) -> Forecast:
        """
        Create a new forecast using a trained model.

        Args:
            forecast_data: Forecast configuration
            current_user: Current authenticated user

        Returns:
            Forecast: Created forecast instance
        """
        try:
            # Validate model exists and is trained
            model = (
                self.db.query(ForecastModel)
                .filter(
                    ForecastModel.id == forecast_data.model_id,
                    ForecastModel.organization_id == current_user.organization_id,
                    ForecastModel.status == "trained",
                )
                .first()
            )

            if not model:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Trained forecast model not found",
                )

            # Create forecast instance
            db_forecast = Forecast(
                model_id=forecast_data.model_id,
                dataset_id=forecast_data.dataset_id,
                name=forecast_data.name,
                description=forecast_data.description,
                forecast_horizon=forecast_data.forecast_horizon,
                forecast_frequency=forecast_data.forecast_frequency,
                scenario_type=forecast_data.scenario_type,
                confidence_level=forecast_data.confidence_level,
                status="pending",
            )

            self.db.add(db_forecast)
            self.db.commit()
            self.db.refresh(db_forecast)

            # Generate forecast
            await self._generate_forecast(db_forecast, model, current_user)

            logger.info(f"Forecast created: {db_forecast.id}")
            return db_forecast

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating forecast: {e}")
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create forecast",
            )

    async def run_backtest(
        self, backtest_data: BacktestCreate, current_user: User
    ) -> BacktestResult:
        """
        Run backtesting for a forecast model.

        Args:
            backtest_data: Backtest configuration
            current_user: Current authenticated user

        Returns:
            BacktestResult: Backtest results with performance metrics
        """
        try:
            # Get model and dataset
            model = (
                self.db.query(ForecastModel)
                .filter(
                    ForecastModel.id == backtest_data.model_id,
                    ForecastModel.organization_id == current_user.organization_id,
                )
                .first()
            )

            if not model:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Forecast model not found",
                )

            dataset = (
                self.db.query(Dataset)
                .filter(
                    Dataset.id == backtest_data.dataset_id,
                    Dataset.organization_id == current_user.organization_id,
                )
                .first()
            )

            if not dataset:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Dataset not found"
                )

            # Create backtest result instance
            backtest_result = BacktestResult(
                model_id=backtest_data.model_id,
                dataset_id=backtest_data.dataset_id,
                name=backtest_data.name,
                description=backtest_data.description,
                backtest_start_date=backtest_data.backtest_start_date,
                backtest_end_date=backtest_data.backtest_end_date,
                forecast_horizon=backtest_data.forecast_horizon,
                cross_validation_folds=backtest_data.cross_validation_folds,
                status="pending",
            )

            self.db.add(backtest_result)
            self.db.commit()
            self.db.refresh(backtest_result)

            # Run backtesting
            await self._run_backtesting(backtest_result, model, dataset)

            logger.info(f"Backtest completed: {backtest_result.id}")
            return backtest_result

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error running backtest: {e}")
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to run backtest",
            )

    async def _load_dataset(self, dataset: Dataset) -> pd.DataFrame:
        """Load dataset from file."""
        try:
            if dataset.file_type == "csv":
                df = pd.read_csv(dataset.file_path)
            elif dataset.file_type in ["xlsx", "xls"]:
                df = pd.read_excel(dataset.file_path)
            else:
                raise ValueError(f"Unsupported file type: {dataset.file_type}")

            return df

        except Exception as e:
            logger.error(f"Error loading dataset: {e}")
            raise

    def _prepare_training_data(
        self, df: pd.DataFrame, model: ForecastModel
    ) -> pd.DataFrame:
        """Prepare data for training based on model configuration."""
        try:
            # Ensure target column exists
            if model.target_column not in df.columns:
                raise ValueError(
                    f"Target column '{model.target_column}' not found in dataset"
                )

            # Filter date range if specified
            if model.training_data_start or model.training_data_end:
                # Try to find date column
                date_columns = df.select_dtypes(include=["datetime64"]).columns
                if len(date_columns) == 0:
                    # Try to convert first column to datetime
                    try:
                        df.iloc[:, 0] = pd.to_datetime(df.iloc[:, 0])
                        date_column = df.columns[0]
                    except:
                        raise ValueError("No date column found for time series data")
                else:
                    date_column = date_columns[0]

                if model.training_data_start:
                    df = df[df[date_column] >= model.training_data_start]
                if model.training_data_end:
                    df = df[df[date_column] <= model.training_data_end]

            # Remove rows with missing target values
            df = df.dropna(subset=[model.target_column])

            return df

        except Exception as e:
            logger.error(f"Error preparing training data: {e}")
            raise

    async def _train_model_by_type(
        self, model: ForecastModel, df: pd.DataFrame
    ) -> Tuple[Any, Dict[str, float]]:
        """Train model based on its type."""
        try:
            if model.model_type == "prophet":
                return await self._train_prophet_model(model, df)
            elif model.model_type == "arima":
                return await self._train_arima_model(model, df)
            elif model.model_type == "linear_regression":
                return await self._train_linear_regression_model(model, df)
            else:
                raise ValueError(f"Unsupported model type: {model.model_type}")

        except Exception as e:
            logger.error(f"Error training {model.model_type} model: {e}")
            raise

    async def _train_prophet_model(
        self, model: ForecastModel, df: pd.DataFrame
    ) -> Tuple[Prophet, Dict[str, float]]:
        """Train Prophet forecasting model."""
        try:
            # Prepare data for Prophet (requires 'ds' and 'y' columns)
            date_columns = df.select_dtypes(include=["datetime64"]).columns
            if len(date_columns) == 0:
                # Try to convert first column to datetime
                df.iloc[:, 0] = pd.to_datetime(df.iloc[:, 0])
                date_column = df.columns[0]
            else:
                date_column = date_columns[0]

            prophet_df = pd.DataFrame(
                {"ds": df[date_column], "y": df[model.target_column]}
            )

            # Configure Prophet with parameters
            prophet_params = model.parameters or {}
            prophet_model = Prophet(
                yearly_seasonality=prophet_params.get("yearly_seasonality", "auto"),
                weekly_seasonality=prophet_params.get("weekly_seasonality", "auto"),
                daily_seasonality=prophet_params.get("daily_seasonality", "auto"),
                seasonality_mode=prophet_params.get("seasonality_mode", "additive"),
                changepoint_prior_scale=prophet_params.get(
                    "changepoint_prior_scale", 0.05
                ),
                seasonality_prior_scale=prophet_params.get(
                    "seasonality_prior_scale", 10.0
                ),
            )

            # Add additional regressors if specified
            if model.features:
                for feature in model.features:
                    if feature in df.columns and feature != model.target_column:
                        prophet_model.add_regressor(feature)
                        prophet_df[feature] = df[feature]

            # Train model
            prophet_model.fit(prophet_df)

            # Calculate validation metrics using cross-validation
            metrics = await self._calculate_prophet_metrics(prophet_model, prophet_df)

            return prophet_model, metrics

        except Exception as e:
            logger.error(f"Error training Prophet model: {e}")
            raise

    async def _train_arima_model(
        self, model: ForecastModel, df: pd.DataFrame
    ) -> Tuple[ARIMA, Dict[str, float]]:
        """Train ARIMA forecasting model."""
        try:
            # Prepare time series data
            ts_data = df[model.target_column].dropna()

            # Get ARIMA parameters
            arima_params = model.parameters or {}
            order = arima_params.get("order", (1, 1, 1))  # (p, d, q)
            seasonal_order = arima_params.get(
                "seasonal_order", (0, 0, 0, 0)
            )  # (P, D, Q, s)

            # Train ARIMA model
            arima_model = ARIMA(ts_data, order=order, seasonal_order=seasonal_order)
            fitted_model = arima_model.fit()

            # Calculate validation metrics
            metrics = await self._calculate_arima_metrics(fitted_model, ts_data)

            return fitted_model, metrics

        except Exception as e:
            logger.error(f"Error training ARIMA model: {e}")
            raise

    async def _train_linear_regression_model(
        self, model: ForecastModel, df: pd.DataFrame
    ) -> Tuple[LinearRegression, Dict[str, float]]:
        """Train Linear Regression forecasting model."""
        try:
            # Prepare features and target
            if not model.features:
                raise ValueError("Linear regression requires feature columns")

            # Check if all features exist
            missing_features = [f for f in model.features if f not in df.columns]
            if missing_features:
                raise ValueError(f"Missing feature columns: {missing_features}")

            X = df[model.features].dropna()
            y = df[model.target_column].loc[X.index]

            # Scale features
            scaler = StandardScaler()
            X_scaled = scaler.fit_transform(X)

            # Train model
            lr_model = LinearRegression()
            lr_model.fit(X_scaled, y)

            # Calculate validation metrics
            y_pred = lr_model.predict(X_scaled)
            metrics = {
                "r_squared": r2_score(y, y_pred),
                "mae": mean_absolute_error(y, y_pred),
                "rmse": np.sqrt(mean_squared_error(y, y_pred)),
                "mape": np.mean(np.abs((y - y_pred) / y)) * 100,
            }

            # Store scaler with model
            lr_model.scaler = scaler

            return lr_model, metrics

        except Exception as e:
            logger.error(f"Error training Linear Regression model: {e}")
            raise

    async def _calculate_prophet_metrics(
        self, model: Prophet, df: pd.DataFrame
    ) -> Dict[str, float]:
        """Calculate validation metrics for Prophet model using cross-validation."""
        try:
            from prophet.diagnostics import cross_validation, performance_metrics

            # Perform cross-validation
            cv_results = cross_validation(
                model,
                initial="730 days",  # 2 years
                period="180 days",  # 6 months
                horizon="365 days",  # 1 year
            )

            # Calculate performance metrics
            metrics_df = performance_metrics(cv_results)

            return {
                "mape": float(metrics_df["mape"].mean()),
                "mae": float(metrics_df["mae"].mean()),
                "rmse": float(metrics_df["rmse"].mean()),
            }

        except Exception as e:
            logger.warning(f"Could not calculate Prophet cross-validation metrics: {e}")
            # Fallback to simple train metrics
            forecast = model.predict(df)
            y_true = df["y"].values
            y_pred = forecast["yhat"].values

            return {
                "mae": float(mean_absolute_error(y_true, y_pred)),
                "rmse": float(np.sqrt(mean_squared_error(y_true, y_pred))),
                "mape": float(np.mean(np.abs((y_true - y_pred) / y_true)) * 100),
            }

    async def _calculate_arima_metrics(
        self, model, ts_data: pd.Series
    ) -> Dict[str, float]:
        """Calculate validation metrics for ARIMA model."""
        try:
            # In-sample forecast
            forecast = model.fittedvalues
            actual = ts_data.iloc[1:]  # Skip first value due to differencing

            # Align forecast and actual data
            min_len = min(len(forecast), len(actual))
            forecast = forecast.iloc[:min_len]
            actual = actual.iloc[:min_len]

            return {
                "mae": float(mean_absolute_error(actual, forecast)),
                "rmse": float(np.sqrt(mean_squared_error(actual, forecast))),
                "mape": float(np.mean(np.abs((actual - forecast) / actual)) * 100),
                "aic": float(model.aic),
                "bic": float(model.bic),
            }

        except Exception as e:
            logger.error(f"Error calculating ARIMA metrics: {e}")
            return {"aic": float(model.aic), "bic": float(model.bic)}

    async def _save_model(self, trained_model: Any, model_id: str) -> Path:
        """Save trained model to disk."""
        try:
            model_path = self.models_dir / f"{model_id}.pkl"

            with open(model_path, "wb") as f:
                pickle.dump(trained_model, f)

            return model_path

        except Exception as e:
            logger.error(f"Error saving model: {e}")
            raise

    async def _load_model(self, model_path: str) -> Any:
        """Load trained model from disk."""
        try:
            with open(model_path, "rb") as f:
                return pickle.load(f)

        except Exception as e:
            logger.error(f"Error loading model: {e}")
            raise

    async def _generate_forecast(
        self, forecast: Forecast, model: ForecastModel, current_user: User
    ):
        """Generate forecast using trained model."""
        try:
            forecast.status = "running"
            forecast.started_at = datetime.utcnow()
            self.db.commit()

            # Load trained model
            trained_model = await self._load_model(model.model_file_path)

            # Load dataset
            dataset = (
                self.db.query(Dataset).filter(Dataset.id == forecast.dataset_id).first()
            )
            df = await self._load_dataset(dataset)

            # Generate forecast based on model type
            if model.model_type == "prophet":
                forecast_data = await self._generate_prophet_forecast(
                    trained_model, df, forecast
                )
            elif model.model_type == "arima":
                forecast_data = await self._generate_arima_forecast(
                    trained_model, df, forecast
                )
            elif model.model_type == "linear_regression":
                forecast_data = await self._generate_lr_forecast(
                    trained_model, df, forecast, model
                )
            else:
                raise ValueError(f"Unsupported model type: {model.model_type}")

            # Update forecast with results
            forecast.forecast_data = forecast_data["predictions"]
            forecast.confidence_intervals = forecast_data.get("confidence_intervals")
            forecast.feature_importance = forecast_data.get("feature_importance")
            forecast.status = "completed"
            forecast.completed_at = datetime.utcnow()

            self.db.commit()

        except Exception as e:
            logger.error(f"Error generating forecast: {e}")
            forecast.status = "failed"
            forecast.error_message = str(e)
            self.db.commit()
            raise

    async def _generate_prophet_forecast(
        self, model: Prophet, df: pd.DataFrame, forecast: Forecast
    ) -> Dict[str, Any]:
        """Generate forecast using Prophet model."""
        try:
            # Prepare future dataframe
            future = model.make_future_dataframe(
                periods=forecast.forecast_horizon,
                freq=forecast.forecast_frequency[0].upper(),
            )

            # Generate forecast
            forecast_result = model.predict(future)

            # Extract forecast data (only future periods)
            future_forecast = forecast_result.tail(forecast.forecast_horizon)

            predictions = []
            for _, row in future_forecast.iterrows():
                predictions.append(
                    {
                        "date": row["ds"].isoformat(),
                        "value": float(row["yhat"]),
                        "lower_bound": float(row["yhat_lower"]),
                        "upper_bound": float(row["yhat_upper"]),
                    }
                )

            return {
                "predictions": predictions,
                "confidence_intervals": {
                    "lower": future_forecast["yhat_lower"].tolist(),
                    "upper": future_forecast["yhat_upper"].tolist(),
                },
            }

        except Exception as e:
            logger.error(f"Error generating Prophet forecast: {e}")
            raise

    async def _generate_arima_forecast(
        self, model, df: pd.DataFrame, forecast: Forecast
    ) -> Dict[str, Any]:
        """Generate forecast using ARIMA model."""
        try:
            # Generate forecast
            forecast_result = model.forecast(steps=forecast.forecast_horizon)
            confidence_intervals = model.get_forecast(
                steps=forecast.forecast_horizon
            ).conf_int()

            # Create date range for forecast
            last_date = (
                df.index[-1]
                if hasattr(df.index, "dtype") and "datetime" in str(df.index.dtype)
                else datetime.now()
            )
            if not isinstance(last_date, datetime):
                last_date = datetime.now()

            freq_map = {
                "daily": "D",
                "weekly": "W",
                "monthly": "M",
                "quarterly": "Q",
                "yearly": "Y",
            }
            freq = freq_map.get(forecast.forecast_frequency, "D")

            future_dates = pd.date_range(
                start=last_date, periods=forecast.forecast_horizon + 1, freq=freq
            )[1:]

            predictions = []
            for i, date in enumerate(future_dates):
                predictions.append(
                    {
                        "date": date.isoformat(),
                        "value": float(forecast_result.iloc[i]),
                        "lower_bound": float(confidence_intervals.iloc[i, 0]),
                        "upper_bound": float(confidence_intervals.iloc[i, 1]),
                    }
                )

            return {
                "predictions": predictions,
                "confidence_intervals": {
                    "lower": confidence_intervals.iloc[:, 0].tolist(),
                    "upper": confidence_intervals.iloc[:, 1].tolist(),
                },
            }

        except Exception as e:
            logger.error(f"Error generating ARIMA forecast: {e}")
            raise

    async def _generate_lr_forecast(
        self,
        model: LinearRegression,
        df: pd.DataFrame,
        forecast: Forecast,
        forecast_model: ForecastModel,
    ) -> Dict[str, Any]:
        """Generate forecast using Linear Regression model."""
        try:
            # For linear regression, we need future feature values
            # This is a simplified approach - in practice, you'd need to forecast features too

            # Use last known feature values and extrapolate
            last_features = df[forecast_model.features].iloc[-1:].values

            predictions = []
            for i in range(forecast.forecast_horizon):
                # Scale features
                X_scaled = model.scaler.transform(last_features)

                # Predict
                pred_value = model.predict(X_scaled)[0]

                # Create date
                last_date = datetime.now()
                freq_map = {
                    "daily": 1,
                    "weekly": 7,
                    "monthly": 30,
                    "quarterly": 90,
                    "yearly": 365,
                }
                days_ahead = (i + 1) * freq_map.get(forecast.forecast_frequency, 1)
                pred_date = last_date + timedelta(days=days_ahead)

                predictions.append(
                    {
                        "date": pred_date.isoformat(),
                        "value": float(pred_value),
                        "lower_bound": None,  # Linear regression doesn't provide confidence intervals by default
                        "upper_bound": None,
                    }
                )

            # Calculate feature importance
            feature_importance = dict(zip(forecast_model.features, abs(model.coef_)))

            return {
                "predictions": predictions,
                "feature_importance": feature_importance,
            }

        except Exception as e:
            logger.error(f"Error generating Linear Regression forecast: {e}")
            raise

    async def _run_backtesting(
        self, backtest: BacktestResult, model: ForecastModel, dataset: Dataset
    ):
        """Run backtesting for model validation."""
        try:
            backtest.status = "running"
            backtest.started_at = datetime.utcnow()
            self.db.commit()

            # Load dataset
            df = await self._load_dataset(dataset)

            # Prepare data for backtesting
            df = self._prepare_training_data(df, model)

            # Filter data to backtest period
            if "date" in df.columns:
                date_col = "date"
            else:
                date_columns = df.select_dtypes(include=["datetime64"]).columns
                if len(date_columns) > 0:
                    date_col = date_columns[0]
                else:
                    # Convert first column to datetime
                    df.iloc[:, 0] = pd.to_datetime(df.iloc[:, 0])
                    date_col = df.columns[0]

            backtest_df = df[
                (df[date_col] >= backtest.backtest_start_date)
                & (df[date_col] <= backtest.backtest_end_date)
            ]

            # Perform time series cross-validation
            metrics = await self._perform_time_series_cv(backtest_df, model, backtest)

            # Update backtest results
            backtest.mape = metrics.get("mape")
            backtest.rmse = metrics.get("rmse")
            backtest.mae = metrics.get("mae")
            backtest.mse = metrics.get("mse")
            backtest.r_squared = metrics.get("r_squared")
            backtest.directional_accuracy = metrics.get("directional_accuracy")
            backtest.predictions_vs_actual = metrics.get("predictions_vs_actual")
            backtest.residuals_analysis = metrics.get("residuals_analysis")
            backtest.performance_by_period = metrics.get("performance_by_period")

            backtest.status = "completed"
            backtest.completed_at = datetime.utcnow()
            backtest.execution_time_seconds = int(
                (backtest.completed_at - backtest.started_at).total_seconds()
            )

            self.db.commit()

        except Exception as e:
            logger.error(f"Error running backtest: {e}")
            backtest.status = "failed"
            backtest.error_message = str(e)
            self.db.commit()
            raise

    async def _perform_time_series_cv(
        self, df: pd.DataFrame, model: ForecastModel, backtest: BacktestResult
    ) -> Dict[str, Any]:
        """Perform time series cross-validation."""
        try:
            # Split data into folds
            n_samples = len(df)
            fold_size = n_samples // backtest.cross_validation_folds

            all_predictions = []
            all_actuals = []
            fold_metrics = []

            for fold in range(backtest.cross_validation_folds):
                # Define train and test splits
                test_start = fold * fold_size
                test_end = min(
                    (fold + 1) * fold_size, n_samples - backtest.forecast_horizon
                )

                if test_end <= test_start:
                    break

                train_df = df.iloc[:test_end]
                test_df = df.iloc[test_end : test_end + backtest.forecast_horizon]

                if len(test_df) == 0:
                    continue

                # Train model on fold data
                fold_model, _ = await self._train_model_by_type(model, train_df)

                # Generate predictions
                if model.model_type == "prophet":
                    predictions = await self._predict_prophet_fold(
                        fold_model, train_df, len(test_df)
                    )
                elif model.model_type == "arima":
                    predictions = await self._predict_arima_fold(
                        fold_model, len(test_df)
                    )
                else:
                    continue  # Skip unsupported models for now

                # Collect results
                actuals = test_df[model.target_column].values
                all_predictions.extend(predictions)
                all_actuals.extend(actuals)

                # Calculate fold metrics
                fold_mape = np.mean(np.abs((actuals - predictions) / actuals)) * 100
                fold_mae = mean_absolute_error(actuals, predictions)
                fold_rmse = np.sqrt(mean_squared_error(actuals, predictions))

                fold_metrics.append(
                    {
                        "fold": fold,
                        "mape": fold_mape,
                        "mae": fold_mae,
                        "rmse": fold_rmse,
                    }
                )

            # Calculate overall metrics
            if len(all_predictions) > 0:
                overall_mape = (
                    np.mean(
                        np.abs(
                            (np.array(all_actuals) - np.array(all_predictions))
                            / np.array(all_actuals)
                        )
                    )
                    * 100
                )
                overall_mae = mean_absolute_error(all_actuals, all_predictions)
                overall_rmse = np.sqrt(mean_squared_error(all_actuals, all_predictions))
                overall_mse = mean_squared_error(all_actuals, all_predictions)
                overall_r2 = r2_score(all_actuals, all_predictions)

                # Calculate directional accuracy
                actual_changes = np.diff(all_actuals)
                pred_changes = np.diff(all_predictions)
                directional_accuracy = (
                    np.mean(np.sign(actual_changes) == np.sign(pred_changes)) * 100
                )

                return {
                    "mape": overall_mape,
                    "mae": overall_mae,
                    "rmse": overall_rmse,
                    "mse": overall_mse,
                    "r_squared": overall_r2,
                    "directional_accuracy": directional_accuracy,
                    "predictions_vs_actual": {
                        "predictions": all_predictions,
                        "actuals": all_actuals,
                    },
                    "performance_by_period": fold_metrics,
                    "residuals_analysis": {
                        "residuals": (
                            np.array(all_actuals) - np.array(all_predictions)
                        ).tolist(),
                        "mean_residual": float(
                            np.mean(np.array(all_actuals) - np.array(all_predictions))
                        ),
                        "std_residual": float(
                            np.std(np.array(all_actuals) - np.array(all_predictions))
                        ),
                    },
                }
            else:
                raise ValueError(
                    "No valid predictions generated during cross-validation"
                )

        except Exception as e:
            logger.error(f"Error performing time series cross-validation: {e}")
            raise

    async def _predict_prophet_fold(
        self, model: Prophet, train_df: pd.DataFrame, horizon: int
    ) -> List[float]:
        """Generate predictions for Prophet model fold."""
        try:
            future = model.make_future_dataframe(periods=horizon, freq="D")
            forecast = model.predict(future)
            return forecast["yhat"].tail(horizon).tolist()
        except Exception as e:
            logger.error(f"Error predicting Prophet fold: {e}")
            return []

    async def _predict_arima_fold(self, model, horizon: int) -> List[float]:
        """Generate predictions for ARIMA model fold."""
        try:
            forecast = model.forecast(steps=horizon)
            return forecast.tolist()
        except Exception as e:
            logger.error(f"Error predicting ARIMA fold: {e}")
            return []
