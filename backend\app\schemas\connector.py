"""
JuliusAI Data Connector Schemas
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from uuid import UUID
from enum import Enum


class SourceType(str, Enum):
    """Data source types."""
    API = "api"
    DATABASE = "database"
    CLOUD_STORAGE = "cloud_storage"
    FILE_SHARE = "file_share"


class Provider(str, Enum):
    """Supported data providers."""
    YAHOO_FINANCE = "yahoo_finance"
    ALPHA_VANTAGE = "alpha_vantage"
    QUANDL = "quandl"
    BLOOMBERG = "bloomberg"
    AWS_S3 = "aws_s3"
    GOOGLE_CLOUD = "google_cloud"
    DROPBOX = "dropbox"
    QUICKBOOKS = "quickbooks"
    XERO = "xero"
    SAP = "sap"
    MYSQL = "mysql"
    POSTGRESQL = "postgresql"
    MONGODB = "mongodb"


class SyncFrequency(str, Enum):
    """Synchronization frequency options."""
    MANUAL = "manual"
    HOURLY = "hourly"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    CUSTOM = "custom"


class SyncStatus(str, Enum):
    """Synchronization status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class CredentialType(str, Enum):
    """API credential types."""
    API_KEY = "api_key"
    OAUTH = "oauth"
    BASIC_AUTH = "basic_auth"
    TOKEN = "token"
    CONNECTION_STRING = "connection_string"


# Data Source Schemas
class DataSourceBase(BaseModel):
    """Base data source schema."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    source_type: SourceType
    provider: Provider
    connection_config: Dict[str, Any]
    data_mapping: Optional[Dict[str, Any]] = None
    transformation_rules: Optional[Dict[str, Any]] = None
    sync_frequency: SyncFrequency = SyncFrequency.MANUAL
    sync_schedule: Optional[str] = None
    auto_sync_enabled: bool = False
    tags: Optional[List[str]] = None


class DataSourceCreate(DataSourceBase):
    """Schema for creating a data source."""
    authentication_config: Optional[Dict[str, Any]] = None
    sync_config: Optional[Dict[str, Any]] = None


class DataSourceUpdate(BaseModel):
    """Schema for updating a data source."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    connection_config: Optional[Dict[str, Any]] = None
    authentication_config: Optional[Dict[str, Any]] = None
    sync_config: Optional[Dict[str, Any]] = None
    data_mapping: Optional[Dict[str, Any]] = None
    transformation_rules: Optional[Dict[str, Any]] = None
    sync_frequency: Optional[SyncFrequency] = None
    sync_schedule: Optional[str] = None
    auto_sync_enabled: Optional[bool] = None
    tags: Optional[List[str]] = None
    is_active: Optional[bool] = None


class DataSource(DataSourceBase):
    """Data source response schema."""
    id: UUID
    organization_id: UUID
    user_id: UUID
    status: str
    last_sync_at: Optional[datetime]
    last_sync_status: Optional[str]
    last_sync_records: int
    last_error_message: Optional[str]
    next_sync_at: Optional[datetime]
    is_active: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Sync Job Schemas
class DataSyncJobBase(BaseModel):
    """Base sync job schema."""
    job_type: str = Field(..., regex="^(full_sync|incremental|test|manual)$")
    sync_parameters: Optional[Dict[str, Any]] = None
    date_range_start: Optional[datetime] = None
    date_range_end: Optional[datetime] = None


class DataSyncJobCreate(DataSyncJobBase):
    """Schema for creating a sync job."""
    data_source_id: UUID


class DataSyncJob(DataSyncJobBase):
    """Sync job response schema."""
    id: UUID
    data_source_id: UUID
    status: SyncStatus
    progress_percentage: int
    records_processed: int
    records_created: int
    records_updated: int
    records_failed: int
    started_at: Optional[datetime]
    completed_at: Optional[datetime]
    error_message: Optional[str]
    error_details: Optional[Dict[str, Any]]
    result_summary: Optional[Dict[str, Any]]
    data_quality_report: Optional[Dict[str, Any]]
    created_at: datetime
    
    # Computed properties
    duration_seconds: Optional[int]
    is_running: bool
    success_rate: float
    
    class Config:
        from_attributes = True


# API Credential Schemas
class APICredentialBase(BaseModel):
    """Base API credential schema."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    provider: Provider
    credential_type: CredentialType


class APICredentialCreate(APICredentialBase):
    """Schema for creating API credentials."""
    credentials: Dict[str, Any] = Field(..., description="Credential data (will be encrypted)")
    expires_at: Optional[datetime] = None


class APICredentialUpdate(BaseModel):
    """Schema for updating API credentials."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    credentials: Optional[Dict[str, Any]] = None
    expires_at: Optional[datetime] = None
    is_active: Optional[bool] = None


class APICredential(APICredentialBase):
    """API credential response schema."""
    id: UUID
    organization_id: UUID
    user_id: UUID
    expires_at: Optional[datetime]
    last_used_at: Optional[datetime]
    usage_count: int
    is_active: bool
    is_verified: bool
    verification_date: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Template Schemas
class DataConnectorTemplateBase(BaseModel):
    """Base connector template schema."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    provider: Provider
    category: Optional[str] = None
    connection_template: Dict[str, Any]
    authentication_template: Dict[str, Any]
    data_mapping_template: Optional[Dict[str, Any]] = None
    sync_config_template: Optional[Dict[str, Any]] = None


class DataConnectorTemplateCreate(DataConnectorTemplateBase):
    """Schema for creating a connector template."""
    supported_operations: Optional[List[str]] = None
    required_credentials: Optional[List[str]] = None
    optional_parameters: Optional[Dict[str, Any]] = None
    data_schema: Optional[Dict[str, Any]] = None


class DataConnectorTemplate(DataConnectorTemplateBase):
    """Connector template response schema."""
    id: UUID
    supported_operations: Optional[List[str]]
    required_credentials: Optional[List[str]]
    optional_parameters: Optional[Dict[str, Any]]
    data_schema: Optional[Dict[str, Any]]
    usage_count: int
    average_rating: float
    is_system_template: bool
    is_active: bool
    version: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Transformation Schemas
class DataTransformationBase(BaseModel):
    """Base transformation schema."""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    transformation_type: str = Field(..., regex="^(mapping|filter|aggregate|calculate|clean)$")
    source_fields: List[Dict[str, Any]]
    target_fields: List[Dict[str, Any]]
    transformation_rules: Dict[str, Any]
    validation_rules: Optional[Dict[str, Any]] = None
    execution_order: int = Field(1, ge=1)
    is_enabled: bool = True
    error_handling: str = Field("skip", regex="^(skip|fail|default_value)$")


class DataTransformationCreate(DataTransformationBase):
    """Schema for creating a transformation."""
    data_source_id: UUID


class DataTransformationUpdate(BaseModel):
    """Schema for updating a transformation."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    description: Optional[str] = None
    source_fields: Optional[List[Dict[str, Any]]] = None
    target_fields: Optional[List[Dict[str, Any]]] = None
    transformation_rules: Optional[Dict[str, Any]] = None
    validation_rules: Optional[Dict[str, Any]] = None
    execution_order: Optional[int] = Field(None, ge=1)
    is_enabled: Optional[bool] = None
    error_handling: Optional[str] = Field(None, regex="^(skip|fail|default_value)$")


class DataTransformation(DataTransformationBase):
    """Transformation response schema."""
    id: UUID
    data_source_id: UUID
    last_execution_time_ms: Optional[int]
    average_execution_time_ms: Optional[int]
    success_rate: float
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Connection Test Schemas
class ConnectionTestRequest(BaseModel):
    """Schema for testing data source connections."""
    source_type: SourceType
    provider: Provider
    connection_config: Dict[str, Any]
    authentication_config: Optional[Dict[str, Any]] = None


class ConnectionTestResult(BaseModel):
    """Connection test result schema."""
    success: bool
    message: str
    response_time_ms: Optional[int] = None
    data_sample: Optional[List[Dict[str, Any]]] = None
    error_details: Optional[Dict[str, Any]] = None
    capabilities: Optional[List[str]] = None


# Sync Configuration Schemas
class SyncConfiguration(BaseModel):
    """Sync configuration schema."""
    frequency: SyncFrequency
    schedule: Optional[str] = None
    auto_sync_enabled: bool = False
    batch_size: int = Field(1000, ge=1, le=10000)
    timeout_seconds: int = Field(300, ge=30, le=3600)
    retry_attempts: int = Field(3, ge=0, le=10)
    retry_delay_seconds: int = Field(60, ge=1, le=300)
    data_retention_days: Optional[int] = Field(None, ge=1)
    enable_data_validation: bool = True
    enable_duplicate_detection: bool = True


# Data Preview Schemas
class DataPreviewRequest(BaseModel):
    """Schema for requesting data preview."""
    data_source_id: UUID
    limit: int = Field(100, ge=1, le=1000)
    filters: Optional[Dict[str, Any]] = None


class DataPreview(BaseModel):
    """Data preview response schema."""
    columns: List[str]
    data: List[Dict[str, Any]]
    total_records: Optional[int] = None
    sample_size: int
    data_types: Dict[str, str]
    preview_generated_at: datetime


# List Schemas
class DataSourceList(BaseModel):
    """Paginated data source list."""
    data_sources: List[DataSource]
    total: int
    page: int
    size: int
    pages: int


class DataSyncJobList(BaseModel):
    """Paginated sync job list."""
    sync_jobs: List[DataSyncJob]
    total: int
    page: int
    size: int
    pages: int


class APICredentialList(BaseModel):
    """Paginated API credential list."""
    credentials: List[APICredential]
    total: int
    page: int
    size: int
    pages: int


class DataConnectorTemplateList(BaseModel):
    """Paginated connector template list."""
    templates: List[DataConnectorTemplate]
    total: int
    page: int
    size: int
    pages: int


# Provider-Specific Schemas
class YahooFinanceConfig(BaseModel):
    """Yahoo Finance specific configuration."""
    symbols: List[str] = Field(..., min_items=1)
    period: str = Field("1y", regex="^(1d|5d|1mo|3mo|6mo|1y|2y|5y|10y|ytd|max)$")
    interval: str = Field("1d", regex="^(1m|2m|5m|15m|30m|60m|90m|1h|1d|5d|1wk|1mo|3mo)$")
    include_dividends: bool = True
    include_splits: bool = True


class AlphaVantageConfig(BaseModel):
    """Alpha Vantage specific configuration."""
    function: str = Field(..., description="API function to call")
    symbol: Optional[str] = None
    market: Optional[str] = None
    outputsize: str = Field("compact", regex="^(compact|full)$")
    datatype: str = Field("json", regex="^(json|csv)$")


class DatabaseConfig(BaseModel):
    """Database connection configuration."""
    host: str
    port: int = Field(..., ge=1, le=65535)
    database: str
    schema: Optional[str] = None
    table: Optional[str] = None
    query: Optional[str] = None
    ssl_enabled: bool = False
    connection_timeout: int = Field(30, ge=1, le=300)


class CloudStorageConfig(BaseModel):
    """Cloud storage configuration."""
    bucket_name: str
    file_path: Optional[str] = None
    file_pattern: Optional[str] = None
    region: Optional[str] = None
    encryption_enabled: bool = False
