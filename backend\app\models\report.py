"""
JuliusAI Report and Analysis Models
"""
import uuid
from sqlalchemy import Column, String, Integer, Float, Boolean, DateTime, Text, ForeignKey, DECIMAL, JSON as JSONB
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Dict, Any, Optional, List

from app.database import Base


class Analysis(Base):
    """Analysis model for storing analysis results and metadata."""
    
    __tablename__ = "analyses"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)
    dataset_id = Column(UUID(as_uuid=True), ForeignKey("datasets.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    analysis_type = Column(String(100), nullable=False)  # descriptive, trend, forecast, anomaly, correlation, custom
    parameters = Column(JSONB, default=dict)
    status = Column(String(50), default="pending", nullable=False)  # pending, running, completed, failed, cancelled
    progress_percentage = Column(Integer, default=0)
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    error_message = Column(Text)
    results = Column(JSONB)  # Analysis results data
    insights = Column(JSONB)  # AI-generated insights
    confidence_score = Column(DECIMAL(3, 2))  # 0.00 to 1.00
    execution_time_ms = Column(Integer)
    tags = Column(JSONB)  # Array of tags
    is_favorite = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    organization = relationship("Organization")
    dataset = relationship("Dataset")
    user = relationship("User")
    metrics = relationship("AnalysisMetric", back_populates="analysis", cascade="all, delete-orphan")
    reports = relationship("Report", back_populates="analysis")
    
    def __repr__(self):
        return f"<Analysis(id={self.id}, name='{self.name}', type='{self.analysis_type}')>"


class AnalysisMetric(Base):
    """Analysis metrics for storing calculated financial and statistical metrics."""
    
    __tablename__ = "analysis_metrics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    analysis_id = Column(UUID(as_uuid=True), ForeignKey("analyses.id"), nullable=False)
    metric_name = Column(String(100), nullable=False)
    metric_value = Column(DECIMAL(20, 6))
    metric_unit = Column(String(50))
    metric_category = Column(String(50))  # financial, statistical, performance, risk
    calculation_method = Column(String(100))
    metadata = Column(JSONB, default=dict)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    analysis = relationship("Analysis", back_populates="metrics")
    
    def __repr__(self):
        return f"<AnalysisMetric(name='{self.metric_name}', value={self.metric_value})>"


class ReportTemplate(Base):
    """Report templates for different audiences and use cases."""
    
    __tablename__ = "report_templates"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    template_type = Column(String(50), nullable=False)  # executive, technical, presentation, dashboard
    audience = Column(String(50), nullable=False)  # executive, analyst, manager, stakeholder
    category = Column(String(50))  # financial, operational, strategic, compliance
    
    # Template Configuration
    template_config = Column(JSONB, nullable=False)  # Template structure and layout
    sections = Column(JSONB, nullable=False)  # Report sections configuration
    styling = Column(JSONB)  # Visual styling preferences
    default_parameters = Column(JSONB)  # Default parameters for report generation
    
    # Template Metadata
    is_public = Column(Boolean, default=False)  # Available to all users in organization
    is_system_template = Column(Boolean, default=False)  # Built-in system template
    usage_count = Column(Integer, default=0)
    last_used_at = Column(DateTime(timezone=True))
    version = Column(String(20), default="1.0")
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    organization = relationship("Organization")
    user = relationship("User")
    reports = relationship("Report", back_populates="template")
    
    def __repr__(self):
        return f"<ReportTemplate(id={self.id}, name='{self.name}', type='{self.template_type}')>"


class Report(Base):
    """Generated reports with content and metadata."""
    
    __tablename__ = "reports"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)
    analysis_id = Column(UUID(as_uuid=True), ForeignKey("analyses.id"), nullable=True)
    template_id = Column(UUID(as_uuid=True), ForeignKey("report_templates.id"), nullable=True)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Report Metadata
    title = Column(String(255), nullable=False)
    description = Column(Text)
    report_type = Column(String(50), nullable=False)  # analysis, forecast, summary, custom
    audience = Column(String(50), nullable=False)  # executive, analyst, manager, stakeholder
    
    # Report Content
    content = Column(JSONB, nullable=False)  # Report content structure
    executive_summary = Column(Text)  # AI-generated executive summary
    key_insights = Column(JSONB)  # Key insights and findings
    recommendations = Column(JSONB)  # AI-generated recommendations
    
    # Export Information
    format = Column(String(20), default="json")  # json, pdf, docx, pptx, html
    file_path = Column(String(500))  # Path to exported file
    file_size = Column(Integer)  # File size in bytes
    
    # Status and Sharing
    status = Column(String(50), default="draft")  # draft, generating, completed, failed
    is_shared = Column(Boolean, default=False)
    share_token = Column(String(100), unique=True)
    share_expires_at = Column(DateTime(timezone=True))
    
    # Generation Metadata
    generation_parameters = Column(JSONB)  # Parameters used for generation
    generation_time_ms = Column(Integer)
    ai_model_used = Column(String(100))  # AI model used for content generation
    generated_at = Column(DateTime(timezone=True))
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    organization = relationship("Organization")
    analysis = relationship("Analysis", back_populates="reports")
    template = relationship("ReportTemplate", back_populates="reports")
    user = relationship("User")
    
    def __repr__(self):
        return f"<Report(id={self.id}, title='{self.title}', type='{self.report_type}')>"


class ReportSection(Base):
    """Individual sections within a report."""
    
    __tablename__ = "report_sections"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    report_id = Column(UUID(as_uuid=True), ForeignKey("reports.id"), nullable=False)
    section_type = Column(String(50), nullable=False)  # summary, analysis, chart, table, text, recommendation
    title = Column(String(255), nullable=False)
    content = Column(JSONB, nullable=False)  # Section content
    order_index = Column(Integer, nullable=False)  # Order within report
    
    # Section Configuration
    styling = Column(JSONB)  # Section-specific styling
    parameters = Column(JSONB)  # Section generation parameters
    data_source = Column(String(100))  # Source of data for this section
    
    # AI Generation Metadata
    ai_generated = Column(Boolean, default=False)
    ai_confidence = Column(DECIMAL(3, 2))  # Confidence in AI-generated content
    human_reviewed = Column(Boolean, default=False)
    reviewed_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    reviewed_at = Column(DateTime(timezone=True))
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    report = relationship("Report")
    reviewer = relationship("User", foreign_keys=[reviewed_by])
    
    def __repr__(self):
        return f"<ReportSection(id={self.id}, title='{self.title}', type='{self.section_type}')>"


class InsightTemplate(Base):
    """Templates for AI-generated insights and recommendations."""
    
    __tablename__ = "insight_templates"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    insight_type = Column(String(50), nullable=False)  # trend, anomaly, correlation, forecast, recommendation
    template_text = Column(Text, nullable=False)  # Template with placeholders
    conditions = Column(JSONB)  # Conditions for when to use this template
    priority = Column(Integer, default=1)  # Priority for template selection
    
    # Template Configuration
    required_metrics = Column(JSONB)  # Required metrics for this insight
    optional_metrics = Column(JSONB)  # Optional metrics that enhance the insight
    audience_level = Column(String(50))  # executive, technical, general
    confidence_threshold = Column(DECIMAL(3, 2), default=0.7)  # Minimum confidence to use
    
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<InsightTemplate(id={self.id}, name='{self.name}', type='{self.insight_type}')>"


class ReportExport(Base):
    """Track report exports and downloads."""
    
    __tablename__ = "report_exports"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    report_id = Column(UUID(as_uuid=True), ForeignKey("reports.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    export_format = Column(String(20), nullable=False)  # pdf, docx, pptx, html, json
    file_path = Column(String(500), nullable=False)
    file_size = Column(Integer)
    export_parameters = Column(JSONB)  # Parameters used for export
    status = Column(String(50), default="pending")  # pending, processing, completed, failed
    error_message = Column(Text)
    download_count = Column(Integer, default=0)
    last_downloaded_at = Column(DateTime(timezone=True))
    expires_at = Column(DateTime(timezone=True))  # When export file expires
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    report = relationship("Report")
    user = relationship("User")
    
    def __repr__(self):
        return f"<ReportExport(id={self.id}, format='{self.export_format}', status='{self.status}')>"
