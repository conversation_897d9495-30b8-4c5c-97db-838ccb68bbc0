"""
JuliusAI Enhanced Security Models
"""
import uuid
from sqlalchemy import <PERSON>um<PERSON>, <PERSON>, Inte<PERSON>, Boolean, DateTime, Text, ForeignKey, JSON as JSONB
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Dict, Any, Optional, List

from app.database import Base


class Role(Base):
    """Role model for RBAC system."""
    
    __tablename__ = "roles"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)
    name = Column(String(100), nullable=False)
    display_name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Role Configuration
    is_system_role = Column(Boolean, default=False, nullable=False)  # Built-in vs custom role
    is_active = Column(Boolean, default=True, nullable=False)
    priority = Column(Integer, default=0)  # Role hierarchy priority (higher = more permissions)
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    organization = relationship("Organization")
    permissions = relationship("RolePermission", back_populates="role", cascade="all, delete-orphan")
    user_roles = relationship("UserRole", back_populates="role", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Role(id={self.id}, name='{self.name}', org={self.organization_id})>"


class Permission(Base):
    """Permission model for granular access control."""
    
    __tablename__ = "permissions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(100), unique=True, nullable=False)
    display_name = Column(String(255), nullable=False)
    description = Column(Text)
    category = Column(String(100), nullable=False)  # data, analysis, reports, admin, etc.
    
    # Permission Configuration
    resource_type = Column(String(100))  # dataset, analysis, report, user, etc.
    action = Column(String(50), nullable=False)  # create, read, update, delete, execute, etc.
    scope = Column(String(50), default="organization")  # organization, own, assigned
    
    # Metadata
    is_system_permission = Column(Boolean, default=True, nullable=False)
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    role_permissions = relationship("RolePermission", back_populates="permission", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Permission(id={self.id}, name='{self.name}', action='{self.action}')>"


class RolePermission(Base):
    """Many-to-many relationship between roles and permissions."""
    
    __tablename__ = "role_permissions"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    role_id = Column(UUID(as_uuid=True), ForeignKey("roles.id"), nullable=False)
    permission_id = Column(UUID(as_uuid=True), ForeignKey("permissions.id"), nullable=False)
    
    # Permission Configuration
    granted = Column(Boolean, default=True, nullable=False)  # Grant or deny permission
    conditions = Column(JSONB)  # Additional conditions for permission
    
    # Metadata
    granted_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    granted_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    role = relationship("Role", back_populates="permissions")
    permission = relationship("Permission", back_populates="role_permissions")
    granted_by_user = relationship("User")
    
    def __repr__(self):
        return f"<RolePermission(role={self.role_id}, permission={self.permission_id}, granted={self.granted})>"


class UserRole(Base):
    """User role assignments with temporal and conditional access."""
    
    __tablename__ = "user_roles"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    role_id = Column(UUID(as_uuid=True), ForeignKey("roles.id"), nullable=False)
    
    # Temporal Access Control
    valid_from = Column(DateTime(timezone=True), server_default=func.now())
    valid_until = Column(DateTime(timezone=True))  # Optional expiration
    
    # Conditional Access
    conditions = Column(JSONB)  # IP restrictions, time-based access, etc.
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Assignment Metadata
    assigned_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    assigned_at = Column(DateTime(timezone=True), server_default=func.now())
    reason = Column(Text)  # Reason for role assignment
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id])
    role = relationship("Role", back_populates="user_roles")
    assigned_by_user = relationship("User", foreign_keys=[assigned_by])
    
    @property
    def is_valid(self) -> bool:
        """Check if role assignment is currently valid."""
        now = datetime.utcnow()
        if not self.is_active:
            return False
        if self.valid_from and now < self.valid_from:
            return False
        if self.valid_until and now > self.valid_until:
            return False
        return True
    
    def __repr__(self):
        return f"<UserRole(user={self.user_id}, role={self.role_id}, active={self.is_active})>"


class MFAMethod(Base):
    """Multi-factor authentication methods for users."""
    
    __tablename__ = "mfa_methods"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    method_type = Column(String(50), nullable=False)  # totp, sms, email, backup_codes
    
    # Method Configuration
    secret_key = Column(Text)  # Encrypted TOTP secret or phone number
    backup_codes = Column(JSONB)  # Encrypted backup codes
    phone_number = Column(String(20))  # For SMS-based MFA
    
    # Status and Metadata
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    last_used_at = Column(DateTime(timezone=True))
    usage_count = Column(Integer, default=0)
    
    # Recovery Information
    recovery_codes_generated_at = Column(DateTime(timezone=True))
    recovery_codes_used = Column(Integer, default=0)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    user = relationship("User", back_populates="mfa_methods")
    
    def __repr__(self):
        return f"<MFAMethod(id={self.id}, user={self.user_id}, type='{self.method_type}')>"


class SecurityEvent(Base):
    """Security events and audit log entries."""
    
    __tablename__ = "security_events"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))  # Nullable for system events
    session_id = Column(UUID(as_uuid=True), ForeignKey("user_sessions.id"))
    
    # Event Classification
    event_type = Column(String(100), nullable=False)  # login, logout, permission_change, data_access, etc.
    event_category = Column(String(50), nullable=False)  # authentication, authorization, data, system
    severity = Column(String(20), default="info")  # info, warning, error, critical
    
    # Event Details
    event_description = Column(Text, nullable=False)
    resource_type = Column(String(100))  # dataset, analysis, report, user, etc.
    resource_id = Column(UUID(as_uuid=True))  # ID of affected resource
    action = Column(String(100))  # create, read, update, delete, login, etc.
    
    # Context Information
    ip_address = Column(String(45))  # IPv4 or IPv6
    user_agent = Column(Text)
    request_id = Column(String(100))  # For request tracing
    additional_data = Column(JSONB)  # Additional event-specific data
    
    # Risk Assessment
    risk_score = Column(Integer, default=0)  # 0-100 risk score
    anomaly_indicators = Column(JSONB)  # Detected anomalies
    
    # Metadata
    timestamp = Column(DateTime(timezone=True), server_default=func.now())
    processed_at = Column(DateTime(timezone=True))  # When event was processed by security system
    
    # Relationships
    organization = relationship("Organization")
    user = relationship("User")
    session = relationship("UserSession")
    
    def __repr__(self):
        return f"<SecurityEvent(id={self.id}, type='{self.event_type}', severity='{self.severity}')>"


class LoginAttempt(Base):
    """Login attempt tracking for security monitoring."""
    
    __tablename__ = "login_attempts"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String(255), nullable=False, index=True)
    ip_address = Column(String(45), nullable=False, index=True)
    user_agent = Column(Text)
    
    # Attempt Details
    success = Column(Boolean, nullable=False)
    failure_reason = Column(String(100))  # invalid_credentials, account_locked, mfa_failed, etc.
    mfa_required = Column(Boolean, default=False)
    mfa_completed = Column(Boolean, default=False)
    
    # Geographic Information
    country = Column(String(100))
    city = Column(String(100))
    timezone = Column(String(50))
    
    # Risk Assessment
    risk_score = Column(Integer, default=0)
    risk_factors = Column(JSONB)  # Detected risk factors
    
    # Metadata
    attempted_at = Column(DateTime(timezone=True), server_default=func.now())
    session_id = Column(UUID(as_uuid=True), ForeignKey("user_sessions.id"))
    
    # Relationships
    session = relationship("UserSession")
    
    def __repr__(self):
        return f"<LoginAttempt(id={self.id}, email='{self.email}', success={self.success})>"


class SecurityPolicy(Base):
    """Organization security policies and configurations."""
    
    __tablename__ = "security_policies"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)
    policy_type = Column(String(100), nullable=False)  # password, mfa, session, access, etc.
    
    # Policy Configuration
    policy_name = Column(String(255), nullable=False)
    policy_description = Column(Text)
    policy_config = Column(JSONB, nullable=False)  # Policy-specific configuration
    
    # Status and Enforcement
    is_active = Column(Boolean, default=True, nullable=False)
    is_enforced = Column(Boolean, default=True, nullable=False)
    enforcement_level = Column(String(20), default="strict")  # strict, moderate, advisory
    
    # Metadata
    created_by = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    last_reviewed_at = Column(DateTime(timezone=True))
    
    # Relationships
    organization = relationship("Organization")
    created_by_user = relationship("User")
    
    def __repr__(self):
        return f"<SecurityPolicy(id={self.id}, type='{self.policy_type}', org={self.organization_id})>"


class AccessRequest(Base):
    """Access requests for elevated permissions or resources."""
    
    __tablename__ = "access_requests"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)
    requester_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    
    # Request Details
    request_type = Column(String(100), nullable=False)  # role_assignment, resource_access, permission_grant
    resource_type = Column(String(100))  # dataset, analysis, report, role, etc.
    resource_id = Column(UUID(as_uuid=True))
    requested_permissions = Column(JSONB)  # Specific permissions requested
    
    # Request Justification
    business_justification = Column(Text, nullable=False)
    urgency_level = Column(String(20), default="normal")  # low, normal, high, critical
    access_duration = Column(String(50))  # temporary, permanent, specific_date
    valid_until = Column(DateTime(timezone=True))  # For temporary access
    
    # Approval Workflow
    status = Column(String(50), default="pending")  # pending, approved, rejected, expired
    approver_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))
    approval_notes = Column(Text)
    approved_at = Column(DateTime(timezone=True))
    
    # Metadata
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    organization = relationship("Organization")
    requester = relationship("User", foreign_keys=[requester_id])
    approver = relationship("User", foreign_keys=[approver_id])
    
    def __repr__(self):
        return f"<AccessRequest(id={self.id}, type='{self.request_type}', status='{self.status}')>"
