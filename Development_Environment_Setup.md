# JuliusAI Development Environment Setup

**Version:** 1.0
**Date:** 2025-01-27
**Author:** Development Team
**Status:** Ready for Implementation

## Table of Contents
1. [Prerequisites](#prerequisites)
2. [Local Development Setup](#local-development-setup)
3. [Docker Environment](#docker-environment)
4. [IDE Configuration](#ide-configuration)
5. [Database Setup](#database-setup)
6. [Environment Variables](#environment-variables)
7. [Testing Setup](#testing-setup)
8. [Troubleshooting](#troubleshooting)

---

## 1. Prerequisites

### 1.1 Required Software
- **Node.js:** v18.x or higher
- **Python:** v3.11 or higher
- **Docker:** v24.x or higher
- **Docker Compose:** v2.x or higher
- **Git:** v2.x or higher
- **VS Code:** Latest version (recommended IDE)

### 1.2 Recommended Tools
- **Postman:** API testing and development
- **DBeaver:** Database management and visualization
- **GitHub Desktop:** Git GUI (optional)
- **Figma:** Design collaboration (for UI/UX work)

### 1.3 System Requirements
- **RAM:** Minimum 8GB, Recommended 16GB
- **Storage:** Minimum 20GB free space
- **OS:** Windows 10/11, macOS 10.15+, or Ubuntu 20.04+

---

## 2. Local Development Setup

### 2.1 Repository Setup
```bash
# Clone the repository
git clone https://github.com/juliusai/juliusai-platform.git
cd juliusai-platform

# Install pre-commit hooks
pip install pre-commit
pre-commit install

# Set up Git configuration
git config user.name "Your Name"
git config user.email "<EMAIL>"
```

### 2.2 Frontend Setup (React)
```bash
# Navigate to frontend directory
cd frontend

# Install dependencies
npm install

# Start development server
npm run dev

# Available scripts:
# npm run dev          - Start development server
# npm run build        - Build for production
# npm run test         - Run tests
# npm run lint         - Run ESLint
# npm run type-check   - Run TypeScript checks
```

### 2.3 Backend Setup (Python/FastAPI)
```bash
# Navigate to backend directory
cd backend

# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt

# Start development server
uvicorn main:app --reload --host 0.0.0.0 --port 8000

# Available commands:
# uvicorn main:app --reload     - Start development server
# pytest                        - Run tests
# black .                       - Format code
# flake8                        - Lint code
# mypy .                        - Type checking
```

---

## 3. Docker Environment

### 3.1 Docker Compose Setup
```yaml
# docker-compose.yml
version: '3.8'

services:
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - REACT_APP_API_URL=http://localhost:8000

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    ports:
      - "8000:8000"
    volumes:
      - ./backend:/app
    environment:
      - DATABASE_URL=**************************************/juliusai
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=juliusai
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - frontend
      - backend

volumes:
  postgres_data:
```

### 3.2 Docker Commands
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f

# Stop all services
docker-compose down

# Rebuild services
docker-compose build

# Run database migrations
docker-compose exec backend alembic upgrade head

# Access database shell
docker-compose exec db psql -U postgres -d juliusai
```

---

## 4. IDE Configuration

### 4.1 VS Code Extensions
```json
{
  "recommendations": [
    "ms-python.python",
    "ms-python.black-formatter",
    "ms-python.flake8",
    "ms-python.mypy-type-checker",
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "ms-vscode.vscode-typescript-next",
    "ms-vscode-remote.remote-containers",
    "ms-vscode.vscode-json",
    "redhat.vscode-yaml",
    "ms-vscode.vscode-docker"
  ]
}
```

### 4.2 VS Code Settings
```json
{
  "python.defaultInterpreterPath": "./backend/venv/bin/python",
  "python.formatting.provider": "black",
  "python.linting.enabled": true,
  "python.linting.flake8Enabled": true,
  "python.linting.mypyEnabled": true,
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.organizeImports": true
  },
  "typescript.preferences.importModuleSpecifier": "relative",
  "eslint.workingDirectories": ["frontend"],
  "prettier.configPath": "./frontend/.prettierrc"
}
```

### 4.3 Debugging Configuration
```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "Python: FastAPI",
      "type": "python",
      "request": "launch",
      "program": "${workspaceFolder}/backend/main.py",
      "console": "integratedTerminal",
      "cwd": "${workspaceFolder}/backend",
      "env": {
        "PYTHONPATH": "${workspaceFolder}/backend"
      }
    },
    {
      "name": "React: Debug",
      "type": "node",
      "request": "launch",
      "cwd": "${workspaceFolder}/frontend",
      "runtimeExecutable": "npm",
      "runtimeArgs": ["run", "dev"]
    }
  ]
}
```

---

## 5. Database Setup

### 5.1 PostgreSQL Configuration
```sql
-- Create development database
CREATE DATABASE juliusai_dev;
CREATE DATABASE juliusai_test;

-- Create user with appropriate permissions
CREATE USER juliusai_user WITH PASSWORD 'dev_password';
GRANT ALL PRIVILEGES ON DATABASE juliusai_dev TO juliusai_user;
GRANT ALL PRIVILEGES ON DATABASE juliusai_test TO juliusai_user;
```

### 5.2 Database Migrations
```bash
# Initialize Alembic (first time only)
cd backend
alembic init alembic

# Create new migration
alembic revision --autogenerate -m "Initial migration"

# Apply migrations
alembic upgrade head

# Downgrade migration
alembic downgrade -1
```

### 5.3 Sample Data Setup
```bash
# Load sample data for development
python scripts/load_sample_data.py

# Reset database (development only)
python scripts/reset_database.py
```

---

## 6. Environment Variables

### 6.1 Backend Environment (.env)
```bash
# Database
DATABASE_URL=postgresql://postgres:password@localhost:5432/juliusai_dev
TEST_DATABASE_URL=postgresql://postgres:password@localhost:5432/juliusai_test

# Redis
REDIS_URL=redis://localhost:6379

# Security
SECRET_KEY=your-secret-key-here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# External APIs
OPENAI_API_KEY=your-openai-api-key
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password

# Development
DEBUG=True
LOG_LEVEL=DEBUG
```

### 6.2 Frontend Environment (.env.local)
```bash
# API Configuration
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000

# Authentication
REACT_APP_AUTH_DOMAIN=your-auth-domain
REACT_APP_AUTH_CLIENT_ID=your-client-id

# Analytics
REACT_APP_GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX

# Development
REACT_APP_ENV=development
REACT_APP_DEBUG=true
```

---

## 7. Testing Setup

### 7.1 Backend Testing
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=app --cov-report=html

# Run specific test file
pytest tests/test_auth.py

# Run tests in watch mode
pytest-watch
```

### 7.2 Frontend Testing
```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run E2E tests
npm run test:e2e

# Update snapshots
npm test -- --updateSnapshot
```

### 7.3 Integration Testing
```bash
# Start test environment
docker-compose -f docker-compose.test.yml up -d

# Run integration tests
npm run test:integration

# Cleanup test environment
docker-compose -f docker-compose.test.yml down
```

---

## 8. Troubleshooting

### 8.1 Common Issues

#### Port Already in Use
```bash
# Find process using port
lsof -i :3000  # or :8000

# Kill process
kill -9 <PID>
```

#### Database Connection Issues
```bash
# Check PostgreSQL status
brew services list | grep postgresql  # macOS
sudo systemctl status postgresql      # Linux

# Restart PostgreSQL
brew services restart postgresql      # macOS
sudo systemctl restart postgresql     # Linux
```

#### Python Virtual Environment Issues
```bash
# Recreate virtual environment
rm -rf venv
python -m venv venv
source venv/bin/activate  # or venv\Scripts\activate on Windows
pip install -r requirements.txt
```

#### Node.js Dependency Issues
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and reinstall
rm -rf node_modules package-lock.json
npm install
```

### 8.2 Performance Optimization
- Use `npm ci` instead of `npm install` in CI/CD
- Enable Docker BuildKit for faster builds
- Use `.dockerignore` to exclude unnecessary files
- Configure IDE to exclude `node_modules` from indexing

### 8.3 Getting Help
- **Internal Documentation:** Check project wiki
- **Team Chat:** Use designated development channel
- **Issue Tracking:** Create GitHub issues for bugs
- **Code Reviews:** Request reviews for all changes

---

## Next Steps

1. **Team Onboarding:** Share this guide with all developers
2. **Environment Testing:** Validate setup across different machines
3. **CI/CD Integration:** Configure automated testing and deployment
4. **Documentation Updates:** Keep this guide current with changes
5. **Security Review:** Ensure all secrets are properly managed

---

**Development Team Contacts:**
- Tech Lead: [To be assigned]
- DevOps Engineer: [To be assigned]
- Senior Developer: [To be assigned]
