# JuliusAI API Specification Document

**Version:** 2.0
**Date:** 2025-01-27
**Phase:** Phase 2 - System Design & Architecture
**Format:** OpenAPI 3.0.3

## Table of Contents
1. [API Overview](#api-overview)
2. [Authentication & Authorization](#authentication--authorization)
3. [Core API Endpoints](#core-api-endpoints)
4. [Data Models](#data-models)
5. [Error Handling](#error-handling)
6. [Rate Limiting](#rate-limiting)
7. [WebSocket Specifications](#websocket-specifications)

---

## 1. API Overview

### 1.1 OpenAPI Specification Header
```yaml
openapi: 3.0.3
info:
  title: JuliusAI API
  description: AI-powered financial data analysis platform API
  version: 2.0.0
  contact:
    name: JuliusAI Development Team
    email: <EMAIL>
  license:
    name: Proprietary
servers:
  - url: https://api.juliusai.com/v1
    description: Production server
  - url: https://staging-api.juliusai.com/v1
    description: Staging server
  - url: http://localhost:8000/v1
    description: Development server

security:
  - BearerAuth: []
  - ApiKeyAuth: []
```

### 1.2 API Design Principles
- **RESTful Design:** Standard HTTP methods and status codes
- **Resource-Based URLs:** Clear, hierarchical resource structure
- **Stateless:** Each request contains all necessary information
- **Versioned:** URL-based versioning for backward compatibility
- **Consistent:** Uniform response formats and error handling
- **Secure:** Authentication required for all endpoints

### 1.3 Base URL Structure
```
https://api.juliusai.com/v1/{resource}/{id?}/{action?}
```

---

## 2. Authentication & Authorization

### 2.1 Security Schemes
```yaml
components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: JWT token obtained from /auth/login
    ApiKeyAuth:
      type: apiKey
      in: header
      name: X-API-Key
      description: API key for service-to-service communication
```

### 2.2 Authentication Endpoints
```yaml
paths:
  /auth/login:
    post:
      summary: User authentication
      tags: [Authentication]
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [email, password]
              properties:
                email:
                  type: string
                  format: email
                  example: "<EMAIL>"
                password:
                  type: string
                  format: password
                  example: "SecurePassword123!"
                remember_me:
                  type: boolean
                  default: false
      responses:
        '200':
          description: Authentication successful
          content:
            application/json:
              schema:
                type: object
                properties:
                  access_token:
                    type: string
                    example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                  refresh_token:
                    type: string
                    example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
                  token_type:
                    type: string
                    example: "bearer"
                  expires_in:
                    type: integer
                    example: 900
                  user:
                    $ref: '#/components/schemas/User'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '422':
          $ref: '#/components/responses/ValidationError'

  /auth/refresh:
    post:
      summary: Refresh access token
      tags: [Authentication]
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [refresh_token]
              properties:
                refresh_token:
                  type: string
      responses:
        '200':
          description: Token refreshed successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  access_token:
                    type: string
                  expires_in:
                    type: integer

  /auth/logout:
    post:
      summary: User logout
      tags: [Authentication]
      responses:
        '200':
          description: Logout successful
        '401':
          $ref: '#/components/responses/Unauthorized'
```

---

## 3. Core API Endpoints

### 3.1 User Management
```yaml
  /users/profile:
    get:
      summary: Get current user profile
      tags: [Users]
      responses:
        '200':
          description: User profile retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
    
    put:
      summary: Update user profile
      tags: [Users]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                first_name:
                  type: string
                  maxLength: 100
                last_name:
                  type: string
                  maxLength: 100
                phone:
                  type: string
                  maxLength: 20
                timezone:
                  type: string
                  example: "America/New_York"
                preferences:
                  type: object
      responses:
        '200':
          description: Profile updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'

  /organizations/{org_id}:
    get:
      summary: Get organization details
      tags: [Organizations]
      parameters:
        - name: org_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Organization details retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organization'
```

### 3.2 Data Management
```yaml
  /datasets:
    get:
      summary: List user datasets
      tags: [Data]
      parameters:
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 100
            default: 20
        - name: status
          in: query
          schema:
            type: string
            enum: [uploaded, processing, processed, failed, archived]
        - name: search
          in: query
          schema:
            type: string
            maxLength: 255
      responses:
        '200':
          description: Datasets retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Dataset'
                  pagination:
                    $ref: '#/components/schemas/Pagination'

    post:
      summary: Upload new dataset
      tags: [Data]
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required: [file]
              properties:
                file:
                  type: string
                  format: binary
                  description: CSV, Excel, or JSON file
                name:
                  type: string
                  maxLength: 255
                description:
                  type: string
                  maxLength: 1000
                tags:
                  type: array
                  items:
                    type: string
      responses:
        '201':
          description: Dataset uploaded successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Dataset'
        '413':
          description: File too large
        '415':
          description: Unsupported file type

  /datasets/{dataset_id}:
    get:
      summary: Get dataset details
      tags: [Data]
      parameters:
        - name: dataset_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Dataset details retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DatasetDetailed'

    delete:
      summary: Delete dataset
      tags: [Data]
      parameters:
        - name: dataset_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '204':
          description: Dataset deleted successfully
        '404':
          $ref: '#/components/responses/NotFound'

  /datasets/{dataset_id}/process:
    post:
      summary: Trigger dataset processing
      tags: [Data]
      parameters:
        - name: dataset_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        content:
          application/json:
            schema:
              type: object
              properties:
                processing_options:
                  type: object
                  properties:
                    auto_detect_types:
                      type: boolean
                      default: true
                    clean_data:
                      type: boolean
                      default: true
                    generate_summary:
                      type: boolean
                      default: true
      responses:
        '202':
          description: Processing started
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ProcessingJob'
```

### 3.3 Analysis Endpoints
```yaml
  /analyses:
    get:
      summary: List user analyses
      tags: [Analysis]
      parameters:
        - name: dataset_id
          in: query
          schema:
            type: string
            format: uuid
        - name: analysis_type
          in: query
          schema:
            type: string
            enum: [descriptive, trend, forecast, anomaly, correlation, custom]
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
        - name: limit
          in: query
          schema:
            type: integer
            minimum: 1
            maximum: 50
            default: 20
      responses:
        '200':
          description: Analyses retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Analysis'
                  pagination:
                    $ref: '#/components/schemas/Pagination'

    post:
      summary: Create new analysis
      tags: [Analysis]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [dataset_id, analysis_type]
              properties:
                dataset_id:
                  type: string
                  format: uuid
                name:
                  type: string
                  maxLength: 255
                description:
                  type: string
                  maxLength: 1000
                analysis_type:
                  type: string
                  enum: [descriptive, trend, forecast, anomaly, correlation, custom]
                parameters:
                  type: object
                  description: Analysis-specific parameters
      responses:
        '201':
          description: Analysis created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Analysis'

  /analyses/{analysis_id}:
    get:
      summary: Get analysis details and results
      tags: [Analysis]
      parameters:
        - name: analysis_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      responses:
        '200':
          description: Analysis details retrieved
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AnalysisDetailed'

  /analyses/{analysis_id}/forecast:
    post:
      summary: Generate forecast from analysis
      tags: [Analysis]
      parameters:
        - name: analysis_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [horizon]
              properties:
                horizon:
                  type: integer
                  minimum: 1
                  maximum: 365
                  description: Forecast horizon in days
                model_type:
                  type: string
                  enum: [linear, arima, prophet, lstm, ensemble]
                  default: prophet
                confidence_level:
                  type: number
                  minimum: 0.5
                  maximum: 0.99
                  default: 0.95
      responses:
        '201':
          description: Forecast generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Forecast'
```

### 3.4 Reporting Endpoints
```yaml
  /reports:
    get:
      summary: List user reports
      tags: [Reports]
      parameters:
        - name: analysis_id
          in: query
          schema:
            type: string
            format: uuid
        - name: status
          in: query
          schema:
            type: string
            enum: [draft, generating, completed, failed, archived]
        - name: page
          in: query
          schema:
            type: integer
            minimum: 1
            default: 1
      responses:
        '200':
          description: Reports retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/Report'
                  pagination:
                    $ref: '#/components/schemas/Pagination'

    post:
      summary: Create new report
      tags: [Reports]
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [title, analysis_id]
              properties:
                title:
                  type: string
                  maxLength: 255
                description:
                  type: string
                  maxLength: 1000
                analysis_id:
                  type: string
                  format: uuid
                template_id:
                  type: string
                  format: uuid
                format:
                  type: string
                  enum: [json, pdf, excel, html]
                  default: json
      responses:
        '201':
          description: Report created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Report'

  /reports/{report_id}/export:
    post:
      summary: Export report in specified format
      tags: [Reports]
      parameters:
        - name: report_id
          in: path
          required: true
          schema:
            type: string
            format: uuid
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required: [format]
              properties:
                format:
                  type: string
                  enum: [pdf, excel, png, html]
                options:
                  type: object
                  properties:
                    include_charts:
                      type: boolean
                      default: true
                    page_size:
                      type: string
                      enum: [A4, Letter, Legal]
                      default: A4
      responses:
        '200':
          description: Export file generated
          content:
            application/octet-stream:
              schema:
                type: string
                format: binary
```

---

## 4. Data Models

### 4.1 Core Schemas
```yaml
components:
  schemas:
    User:
      type: object
      properties:
        id:
          type: string
          format: uuid
        email:
          type: string
          format: email
        first_name:
          type: string
        last_name:
          type: string
        role:
          type: string
          enum: [admin, manager, analyst, viewer]
        organization_id:
          type: string
          format: uuid
        avatar_url:
          type: string
          format: uri
        timezone:
          type: string
        language:
          type: string
        preferences:
          type: object
        last_login_at:
          type: string
          format: date-time
        created_at:
          type: string
          format: date-time
        is_active:
          type: boolean

    Organization:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        slug:
          type: string
        subscription_tier:
          type: string
          enum: [basic, professional, enterprise]
        max_users:
          type: integer
        max_datasets:
          type: integer
        storage_limit_gb:
          type: integer
        settings:
          type: object
        created_at:
          type: string
          format: date-time
        is_active:
          type: boolean

    Dataset:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        description:
          type: string
        file_name:
          type: string
        file_size:
          type: integer
        file_type:
          type: string
        row_count:
          type: integer
        column_count:
          type: integer
        status:
          type: string
          enum: [uploaded, processing, processed, failed, archived]
        data_quality_score:
          type: number
          minimum: 0
          maximum: 1
        tags:
          type: array
          items:
            type: string
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time

    Analysis:
      type: object
      properties:
        id:
          type: string
          format: uuid
        name:
          type: string
        description:
          type: string
        dataset_id:
          type: string
          format: uuid
        analysis_type:
          type: string
          enum: [descriptive, trend, forecast, anomaly, correlation, custom]
        status:
          type: string
          enum: [pending, running, completed, failed, cancelled]
        progress_percentage:
          type: integer
          minimum: 0
          maximum: 100
        confidence_score:
          type: number
          minimum: 0
          maximum: 1
        execution_time_ms:
          type: integer
        created_at:
          type: string
          format: date-time
        completed_at:
          type: string
          format: date-time

    Pagination:
      type: object
      properties:
        page:
          type: integer
        limit:
          type: integer
        total:
          type: integer
        pages:
          type: integer
        has_next:
          type: boolean
        has_prev:
          type: boolean
```

---

## 5. Error Handling

### 5.1 Standard Error Response
```yaml
components:
  responses:
    BadRequest:
      description: Bad request
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    
    Unauthorized:
      description: Authentication required
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    
    Forbidden:
      description: Insufficient permissions
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    
    NotFound:
      description: Resource not found
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Error'
    
    ValidationError:
      description: Validation error
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ValidationError'

  schemas:
    Error:
      type: object
      required: [error, message]
      properties:
        error:
          type: string
          example: "RESOURCE_NOT_FOUND"
        message:
          type: string
          example: "The requested dataset was not found"
        details:
          type: object
        request_id:
          type: string
          format: uuid
        timestamp:
          type: string
          format: date-time

    ValidationError:
      type: object
      required: [error, message, validation_errors]
      properties:
        error:
          type: string
          example: "VALIDATION_ERROR"
        message:
          type: string
          example: "Request validation failed"
        validation_errors:
          type: array
          items:
            type: object
            properties:
              field:
                type: string
              message:
                type: string
              code:
                type: string
```

---

## 6. Rate Limiting

### 6.1 Rate Limit Headers
```
X-RateLimit-Limit: 1000
X-RateLimit-Remaining: 999
X-RateLimit-Reset: 1640995200
X-RateLimit-Window: 3600
```

### 6.2 Rate Limit Tiers
- **Basic Tier:** 1,000 requests/hour
- **Professional Tier:** 5,000 requests/hour
- **Enterprise Tier:** 20,000 requests/hour
- **File Upload:** 10 uploads/hour (all tiers)

---

## 7. WebSocket Specifications

### 7.1 Real-time Updates
```javascript
// WebSocket connection for real-time updates
const ws = new WebSocket('wss://api.juliusai.com/v1/ws');

// Message types
{
  "type": "analysis_progress",
  "data": {
    "analysis_id": "uuid",
    "progress": 75,
    "status": "running",
    "estimated_completion": "2025-01-27T15:30:00Z"
  }
}

{
  "type": "analysis_completed",
  "data": {
    "analysis_id": "uuid",
    "status": "completed",
    "results_available": true
  }
}

{
  "type": "anomaly_detected",
  "data": {
    "dataset_id": "uuid",
    "anomaly": {
      "metric": "revenue",
      "severity": "high",
      "description": "Unusual spike detected"
    }
  }
}
```

---

**API Documentation Status:**
- ✅ Core endpoints defined
- ✅ Authentication flow specified
- ✅ Data models documented
- ✅ Error handling standardized
- ✅ Rate limiting configured
- ✅ WebSocket specifications included

**Next Steps:**
1. Generate interactive API documentation (Swagger UI)
2. Create API client SDKs (Python, JavaScript)
3. Implement API testing suite
4. Set up API monitoring and analytics
5. Prepare API versioning strategy
