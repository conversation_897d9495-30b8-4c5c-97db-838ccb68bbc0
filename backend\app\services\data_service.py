"""
JuliusAI Data Ingestion and Management Service
"""
from sqlalchemy.orm import Session
from fastapi import HTTPException, status, UploadFile
from typing import Optional, List, Dict, Any, Tuple
import pandas as pd
import numpy as np
from pathlib import Path
import logging
from datetime import datetime
import mimetypes

from app.models.data import Dataset, DataColumn, DataProcessingJob
from app.models.user import User, Organization
from app.schemas.data import (
    DatasetCreate, DatasetUpdate, DatasetUpload, 
    DataPreview, DataQualityReport, FileUploadResponse
)
from app.services.file_service import FileService
from app.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class DataService:
    """Service for data ingestion and management."""
    
    def __init__(self, db: Session):
        self.db = db
        self.file_service = FileService()
    
    async def upload_dataset(
        self, 
        file: UploadFile, 
        upload_data: DatasetUpload,
        current_user: User
    ) -> FileUploadResponse:
        """
        Upload and process a dataset file.
        
        Args:
            file: Uploaded file
            upload_data: Upload metadata
            current_user: Current authenticated user
            
        Returns:
            FileUploadResponse: Upload result
        """
        try:
            # Validate file
            is_valid, error_message = await self.file_service.validate_file(file)
            if not is_valid:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=error_message
                )
            
            # Save file
            file_path, checksum, file_size = await self.file_service.save_file(
                file, str(current_user.organization_id), str(current_user.id)
            )
            
            # Get file info
            mime_type, _ = mimetypes.guess_type(file.filename)
            file_extension = Path(file.filename).suffix.lower().lstrip('.')
            
            # Create dataset record
            dataset_name = upload_data.name or Path(file.filename).stem
            dataset = Dataset(
                organization_id=current_user.organization_id,
                user_id=current_user.id,
                name=dataset_name,
                description=upload_data.description,
                file_name=file.filename,
                file_path=file_path,
                file_size=file_size,
                file_type=file_extension,
                mime_type=mime_type,
                checksum=checksum,
                tags=upload_data.tags,
                status="uploaded"
            )
            
            self.db.add(dataset)
            self.db.flush()  # Get dataset ID
            
            # Create processing job if auto_process is enabled
            processing_job = None
            if upload_data.auto_process:
                processing_job = DataProcessingJob(
                    dataset_id=dataset.id,
                    job_type="parse",
                    status="pending"
                )
                self.db.add(processing_job)
                self.db.flush()
            
            self.db.commit()
            
            logger.info(f"Dataset uploaded successfully: {dataset.id}")
            
            return FileUploadResponse(
                dataset_id=dataset.id,
                file_name=file.filename,
                file_size=file_size,
                file_type=file_extension,
                status="uploaded",
                message="File uploaded successfully",
                processing_job_id=processing_job.id if processing_job else None
            )
            
        except HTTPException:
            self.db.rollback()
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"Dataset upload error: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Upload failed: {str(e)}"
            )
    
    def get_datasets(
        self, 
        current_user: User,
        skip: int = 0,
        limit: int = 20,
        status_filter: Optional[str] = None,
        search: Optional[str] = None
    ) -> Tuple[List[Dataset], int]:
        """
        Get datasets for current user's organization.
        
        Args:
            current_user: Current authenticated user
            skip: Number of records to skip
            limit: Maximum number of records to return
            status_filter: Filter by dataset status
            search: Search term for dataset name
            
        Returns:
            Tuple[List[Dataset], int]: (datasets, total_count)
        """
        try:
            query = self.db.query(Dataset).filter(
                Dataset.organization_id == current_user.organization_id
            )
            
            # Apply filters
            if status_filter:
                query = query.filter(Dataset.status == status_filter)
            
            if search:
                query = query.filter(
                    Dataset.name.ilike(f"%{search}%")
                )
            
            # Get total count
            total_count = query.count()
            
            # Apply pagination and ordering
            datasets = query.order_by(Dataset.created_at.desc()).offset(skip).limit(limit).all()
            
            return datasets, total_count
            
        except Exception as e:
            logger.error(f"Error getting datasets: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve datasets"
            )
    
    def get_dataset(self, dataset_id: str, current_user: User) -> Dataset:
        """
        Get dataset by ID.
        
        Args:
            dataset_id: Dataset ID
            current_user: Current authenticated user
            
        Returns:
            Dataset: Dataset object
        """
        try:
            dataset = self.db.query(Dataset).filter(
                Dataset.id == dataset_id,
                Dataset.organization_id == current_user.organization_id
            ).first()
            
            if not dataset:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Dataset not found"
                )
            
            return dataset
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting dataset: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to retrieve dataset"
            )
    
    def update_dataset(
        self, 
        dataset_id: str, 
        update_data: DatasetUpdate, 
        current_user: User
    ) -> Dataset:
        """
        Update dataset metadata.
        
        Args:
            dataset_id: Dataset ID
            update_data: Update data
            current_user: Current authenticated user
            
        Returns:
            Dataset: Updated dataset
        """
        try:
            dataset = self.get_dataset(dataset_id, current_user)
            
            # Update fields
            if update_data.name is not None:
                dataset.name = update_data.name
            if update_data.description is not None:
                dataset.description = update_data.description
            if update_data.tags is not None:
                dataset.tags = update_data.tags
            if update_data.is_public is not None:
                dataset.is_public = update_data.is_public
            
            dataset.updated_at = datetime.utcnow()
            
            self.db.commit()
            
            logger.info(f"Dataset updated: {dataset_id}")
            return dataset
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating dataset: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to update dataset"
            )
    
    async def delete_dataset(self, dataset_id: str, current_user: User) -> bool:
        """
        Delete dataset and associated file.
        
        Args:
            dataset_id: Dataset ID
            current_user: Current authenticated user
            
        Returns:
            bool: True if deleted successfully
        """
        try:
            dataset = self.get_dataset(dataset_id, current_user)
            
            # Delete file from disk
            if dataset.file_path:
                await self.file_service.delete_file(dataset.file_path)
            
            # Delete dataset record (cascades to related records)
            self.db.delete(dataset)
            self.db.commit()
            
            logger.info(f"Dataset deleted: {dataset_id}")
            return True
            
        except HTTPException:
            raise
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error deleting dataset: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete dataset"
            )
    
    def get_data_preview(self, dataset_id: str, current_user: User, rows: int = 10) -> DataPreview:
        """
        Get preview of dataset data.
        
        Args:
            dataset_id: Dataset ID
            current_user: Current authenticated user
            rows: Number of rows to preview
            
        Returns:
            DataPreview: Data preview
        """
        try:
            dataset = self.get_dataset(dataset_id, current_user)
            
            if not dataset.file_path or not Path(dataset.file_path).exists():
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Dataset file not found"
                )
            
            # Read file based on type
            df = self._read_file(dataset.file_path, dataset.file_type)
            
            # Limit preview rows
            preview_df = df.head(rows)
            
            # Get column information
            columns = list(df.columns)
            data_types = {col: str(df[col].dtype) for col in columns}
            null_counts = {col: int(df[col].isnull().sum()) for col in columns}
            
            # Convert to records for JSON serialization
            sample_data = preview_df.replace({np.nan: None}).to_dict('records')
            
            # Generate summary statistics for numeric columns
            summary_stats = {}
            for col in columns:
                if df[col].dtype in ['int64', 'float64']:
                    summary_stats[col] = {
                        'mean': float(df[col].mean()) if not df[col].isna().all() else None,
                        'std': float(df[col].std()) if not df[col].isna().all() else None,
                        'min': float(df[col].min()) if not df[col].isna().all() else None,
                        'max': float(df[col].max()) if not df[col].isna().all() else None,
                        'median': float(df[col].median()) if not df[col].isna().all() else None
                    }
            
            return DataPreview(
                columns=columns,
                sample_data=sample_data,
                total_rows=len(df),
                data_types=data_types,
                null_counts=null_counts,
                summary_stats=summary_stats
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error getting data preview: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to preview data: {str(e)}"
            )
    
    def _read_file(self, file_path: str, file_type: str) -> pd.DataFrame:
        """
        Read file into pandas DataFrame.
        
        Args:
            file_path: Path to file
            file_type: File type (csv, xlsx, xls)
            
        Returns:
            pd.DataFrame: Loaded data
        """
        try:
            if file_type == 'csv':
                # Try different encodings and separators
                for encoding in ['utf-8', 'latin-1', 'cp1252']:
                    try:
                        df = pd.read_csv(file_path, encoding=encoding)
                        break
                    except UnicodeDecodeError:
                        continue
                else:
                    raise ValueError("Could not decode CSV file with any supported encoding")
                    
            elif file_type in ['xlsx', 'xls']:
                df = pd.read_excel(file_path)
            else:
                raise ValueError(f"Unsupported file type: {file_type}")
            
            return df
            
        except Exception as e:
            logger.error(f"Error reading file {file_path}: {e}")
            raise ValueError(f"Failed to read file: {str(e)}")
