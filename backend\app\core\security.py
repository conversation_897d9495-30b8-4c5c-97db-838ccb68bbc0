"""
JuliusAI Security Utilities
"""
from datetime import datetime, timedelta
from typing import Optional, Union
from jose import JWTError, jwt
from passlib.context import CryptContext
from fastapi import HTTPException, status
import hashlib
import secrets
import logging

from app.config import get_settings
from app.schemas.user import TokenData

settings = get_settings()
logger = logging.getLogger(__name__)

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")


def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT access token.
    
    Args:
        data: Token payload data
        expires_delta: Token expiration time
        
    Returns:
        str: Encoded JWT token
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
    
    to_encode.update({"exp": expire, "type": "access"})
    
    try:
        encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
        return encoded_jwt
    except Exception as e:
        logger.error(f"Error creating access token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not create access token"
        )


def create_refresh_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    """
    Create a JWT refresh token.
    
    Args:
        data: Token payload data
        expires_delta: Token expiration time
        
    Returns:
        str: Encoded JWT refresh token
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(days=settings.refresh_token_expire_days)
    
    to_encode.update({"exp": expire, "type": "refresh"})
    
    try:
        encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
        return encoded_jwt
    except Exception as e:
        logger.error(f"Error creating refresh token: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not create refresh token"
        )


def verify_token(token: str, token_type: str = "access") -> TokenData:
    """
    Verify and decode a JWT token.
    
    Args:
        token: JWT token to verify
        token_type: Expected token type (access or refresh)
        
    Returns:
        TokenData: Decoded token data
        
    Raises:
        HTTPException: If token is invalid or expired
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    
    try:
        payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
        
        # Verify token type
        if payload.get("type") != token_type:
            raise credentials_exception
        
        # Extract token data
        user_id = payload.get("user_id")
        email = payload.get("email")
        organization_id = payload.get("organization_id")
        role = payload.get("role")
        session_id = payload.get("session_id")
        
        if not all([user_id, email, organization_id, role, session_id]):
            raise credentials_exception
        
        token_data = TokenData(
            user_id=user_id,
            email=email,
            organization_id=organization_id,
            role=role,
            session_id=session_id
        )
        
        return token_data
        
    except JWTError as e:
        logger.warning(f"JWT verification failed: {e}")
        raise credentials_exception
    except Exception as e:
        logger.error(f"Token verification error: {e}")
        raise credentials_exception


def hash_password(password: str) -> str:
    """
    Hash a password using bcrypt.
    
    Args:
        password: Plain text password
        
    Returns:
        str: Hashed password
    """
    try:
        return pwd_context.hash(password)
    except Exception as e:
        logger.error(f"Error hashing password: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not hash password"
        )


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """
    Verify a password against its hash.
    
    Args:
        plain_password: Plain text password
        hashed_password: Hashed password
        
    Returns:
        bool: True if password matches, False otherwise
    """
    try:
        return pwd_context.verify(plain_password, hashed_password)
    except Exception as e:
        logger.error(f"Error verifying password: {e}")
        return False


def hash_token(token: str) -> str:
    """
    Hash a token for secure storage.
    
    Args:
        token: Token to hash
        
    Returns:
        str: Hashed token
    """
    return hashlib.sha256(token.encode()).hexdigest()


def generate_secure_token(length: int = 32) -> str:
    """
    Generate a cryptographically secure random token.
    
    Args:
        length: Token length in bytes
        
    Returns:
        str: Secure random token
    """
    return secrets.token_urlsafe(length)


def check_password_strength(password: str) -> dict:
    """
    Check password strength and return feedback.
    
    Args:
        password: Password to check
        
    Returns:
        dict: Password strength analysis
    """
    feedback = {
        "is_strong": True,
        "score": 0,
        "issues": []
    }
    
    # Length check
    if len(password) < 8:
        feedback["issues"].append("Password must be at least 8 characters long")
        feedback["is_strong"] = False
    else:
        feedback["score"] += 1
    
    # Character type checks
    if not any(c.isupper() for c in password):
        feedback["issues"].append("Password must contain at least one uppercase letter")
        feedback["is_strong"] = False
    else:
        feedback["score"] += 1
    
    if not any(c.islower() for c in password):
        feedback["issues"].append("Password must contain at least one lowercase letter")
        feedback["is_strong"] = False
    else:
        feedback["score"] += 1
    
    if not any(c.isdigit() for c in password):
        feedback["issues"].append("Password must contain at least one digit")
        feedback["is_strong"] = False
    else:
        feedback["score"] += 1
    
    if not any(c in "!@#$%^&*(),.?\":{}|<>" for c in password):
        feedback["issues"].append("Password must contain at least one special character")
        feedback["is_strong"] = False
    else:
        feedback["score"] += 1
    
    return feedback
