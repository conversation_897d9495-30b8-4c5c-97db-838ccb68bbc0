"""
JuliusAI User and Authentication Schemas
"""
from pydantic import BaseModel, EmailStr, validator, Field
from typing import Optional, Dict, Any
from datetime import datetime
from uuid import UUID
import re


class OrganizationBase(BaseModel):
    """Base organization schema."""
    name: str = Field(..., min_length=1, max_length=255)
    subscription_tier: str = Field(default="basic", regex="^(basic|professional|enterprise)$")


class OrganizationCreate(OrganizationBase):
    """Schema for creating an organization."""
    slug: str = Field(..., min_length=1, max_length=100)
    
    @validator("slug")
    def validate_slug(cls, v):
        """Validate organization slug format."""
        if not re.match(r"^[a-z0-9-]+$", v):
            raise ValueError("Slug must contain only lowercase letters, numbers, and hyphens")
        return v


class OrganizationUpdate(BaseModel):
    """Schema for updating an organization."""
    name: Optional[str] = Field(None, min_length=1, max_length=255)
    subscription_tier: Optional[str] = Field(None, regex="^(basic|professional|enterprise)$")
    max_users: Optional[int] = Field(None, gt=0)
    max_datasets: Optional[int] = Field(None, gt=0)
    storage_limit_gb: Optional[int] = Field(None, gt=0)
    settings: Optional[Dict[str, Any]] = None


class Organization(OrganizationBase):
    """Organization response schema."""
    id: UUID
    slug: str
    max_users: int
    max_datasets: int
    storage_limit_gb: int
    settings: Dict[str, Any]
    created_at: datetime
    updated_at: datetime
    is_active: bool
    
    class Config:
        from_attributes = True


class UserBase(BaseModel):
    """Base user schema."""
    email: EmailStr
    first_name: str = Field(..., min_length=1, max_length=100)
    last_name: str = Field(..., min_length=1, max_length=100)
    role: str = Field(default="analyst", regex="^(admin|manager|analyst|viewer)$")


class UserCreate(UserBase):
    """Schema for creating a user."""
    password: str = Field(..., min_length=8, max_length=128)
    organization_id: UUID
    
    @validator("password")
    def validate_password(cls, v):
        """Validate password strength."""
        if not re.search(r"[A-Z]", v):
            raise ValueError("Password must contain at least one uppercase letter")
        if not re.search(r"[a-z]", v):
            raise ValueError("Password must contain at least one lowercase letter")
        if not re.search(r"\d", v):
            raise ValueError("Password must contain at least one digit")
        if not re.search(r"[!@#$%^&*(),.?\":{}|<>]", v):
            raise ValueError("Password must contain at least one special character")
        return v


class UserUpdate(BaseModel):
    """Schema for updating a user."""
    first_name: Optional[str] = Field(None, min_length=1, max_length=100)
    last_name: Optional[str] = Field(None, min_length=1, max_length=100)
    phone: Optional[str] = Field(None, max_length=20)
    timezone: Optional[str] = Field(None, max_length=50)
    language: Optional[str] = Field(None, max_length=10)
    preferences: Optional[Dict[str, Any]] = None


class UserPasswordUpdate(BaseModel):
    """Schema for updating user password."""
    current_password: str
    new_password: str = Field(..., min_length=8, max_length=128)
    
    @validator("new_password")
    def validate_password(cls, v):
        """Validate password strength."""
        if not re.search(r"[A-Z]", v):
            raise ValueError("Password must contain at least one uppercase letter")
        if not re.search(r"[a-z]", v):
            raise ValueError("Password must contain at least one lowercase letter")
        if not re.search(r"\d", v):
            raise ValueError("Password must contain at least one digit")
        if not re.search(r"[!@#$%^&*(),.?\":{}|<>]", v):
            raise ValueError("Password must contain at least one special character")
        return v


class User(UserBase):
    """User response schema."""
    id: UUID
    organization_id: UUID
    avatar_url: Optional[str]
    phone: Optional[str]
    timezone: str
    language: str
    preferences: Dict[str, Any]
    last_login_at: Optional[datetime]
    email_verified_at: Optional[datetime]
    created_at: datetime
    updated_at: datetime
    is_active: bool
    
    # Computed properties
    full_name: str
    is_admin: bool
    is_manager: bool
    
    class Config:
        from_attributes = True


class UserLogin(BaseModel):
    """Schema for user login."""
    email: EmailStr
    password: str
    remember_me: bool = False


class UserRegister(UserCreate):
    """Schema for user registration."""
    organization_name: str = Field(..., min_length=1, max_length=255)
    organization_slug: str = Field(..., min_length=1, max_length=100)
    
    @validator("organization_slug")
    def validate_organization_slug(cls, v):
        """Validate organization slug format."""
        if not re.match(r"^[a-z0-9-]+$", v):
            raise ValueError("Organization slug must contain only lowercase letters, numbers, and hyphens")
        return v


class Token(BaseModel):
    """Token response schema."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: User


class TokenRefresh(BaseModel):
    """Schema for token refresh."""
    refresh_token: str


class TokenData(BaseModel):
    """Token payload data."""
    user_id: UUID
    email: str
    organization_id: UUID
    role: str
    session_id: UUID
