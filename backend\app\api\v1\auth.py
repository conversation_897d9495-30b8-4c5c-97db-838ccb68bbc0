"""
JuliusAI Authentication API Endpoints
"""
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import Dict, Any
import logging

from app.database import get_db
from app.schemas.user import User<PERSON><PERSON><PERSON>, User<PERSON>eg<PERSON>, Token, TokenRefresh, User as UserSchema
from app.services.auth_service import AuthService
from app.core.deps import get_current_user_token, get_current_active_user
from app.models.user import User

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/auth", tags=["Authentication"])


@router.post("/register", response_model=Token, status_code=status.HTTP_201_CREATED)
async def register(
    user_data: UserRegister,
    request: Request,
    db: Session = Depends(get_db)
) -> Token:
    """
    Register a new user and organization.
    
    Args:
        user_data: User registration data
        request: FastAPI request object
        db: Database session
        
    Returns:
        Token: Authentication tokens and user data
    """
    try:
        auth_service = AuthService(db)
        
        # Extract client information
        ip_address = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        
        token_response = auth_service.register_user(
            user_data=user_data,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        logger.info(f"User registered successfully: {user_data.email}")
        return token_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Registration endpoint error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/login", response_model=Token)
async def login(
    login_data: UserLogin,
    request: Request,
    db: Session = Depends(get_db)
) -> Token:
    """
    Authenticate user and create session.
    
    Args:
        login_data: User login credentials
        request: FastAPI request object
        db: Database session
        
    Returns:
        Token: Authentication tokens and user data
    """
    try:
        auth_service = AuthService(db)
        
        # Extract client information
        ip_address = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        
        token_response = auth_service.authenticate_user(
            login_data=login_data,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        logger.info(f"User logged in successfully: {login_data.email}")
        return token_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Login endpoint error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/refresh", response_model=Token)
async def refresh_token(
    token_data: TokenRefresh,
    request: Request,
    db: Session = Depends(get_db)
) -> Token:
    """
    Refresh access token using refresh token.
    
    Args:
        token_data: Refresh token data
        request: FastAPI request object
        db: Database session
        
    Returns:
        Token: New authentication tokens
    """
    try:
        auth_service = AuthService(db)
        
        # Extract client information
        ip_address = request.client.host if request.client else None
        user_agent = request.headers.get("user-agent")
        
        token_response = auth_service.refresh_token(
            refresh_token=token_data.refresh_token,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        logger.info("Token refreshed successfully")
        return token_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Token refresh endpoint error: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token refresh failed"
        )


@router.post("/logout", response_model=Dict[str, str])
async def logout(
    token_data = Depends(get_current_user_token),
    db: Session = Depends(get_db)
) -> Dict[str, str]:
    """
    Logout user by deactivating session.
    
    Args:
        token_data: Current user token data
        db: Database session
        
    Returns:
        Dict: Logout confirmation message
    """
    try:
        auth_service = AuthService(db)
        success = auth_service.logout_user(token_data)
        
        if success:
            logger.info(f"User logged out successfully: {token_data.email}")
            return {"message": "Logged out successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Logout failed"
            )
            
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Logout endpoint error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@router.get("/me", response_model=UserSchema)
async def get_current_user_info(
    current_user: User = Depends(get_current_active_user)
) -> UserSchema:
    """
    Get current user information.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        UserSchema: Current user data
    """
    try:
        logger.info(f"User info requested: {current_user.email}")
        return UserSchema.from_orm(current_user)
        
    except Exception as e:
        logger.error(f"Get user info error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not retrieve user information"
        )


@router.get("/verify", response_model=Dict[str, Any])
async def verify_token_endpoint(
    current_user: User = Depends(get_current_active_user)
) -> Dict[str, Any]:
    """
    Verify if current token is valid.
    
    Args:
        current_user: Current authenticated user
        
    Returns:
        Dict: Token verification result
    """
    try:
        return {
            "valid": True,
            "user_id": str(current_user.id),
            "email": current_user.email,
            "role": current_user.role,
            "organization_id": str(current_user.organization_id)
        }
        
    except Exception as e:
        logger.error(f"Token verification error: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Token verification failed"
        )
