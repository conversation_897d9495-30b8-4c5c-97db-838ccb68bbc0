"""
JuliusAI Enhanced Security Service
"""

import pyotp
import qrcode
import io
import base64
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON>Exception, status, Request
from typing import Optional, List, Dict, Any, Tuple
import logging
from datetime import datetime, timedelta
import secrets
import hashlib
import json
from cryptography.fernet import Fernet

from app.models.security import (
    Role,
    Permission,
    RolePermission,
    UserRole,
    MFAMethod,
    SecurityEvent,
    LoginAttempt,
    SecurityPolicy,
    AccessRequest,
)
from app.models.user import User, Organization
from app.schemas.security import (
    RoleCreate,
    UserRoleCreate,
    MFAMethodCreate,
    SecurityEventCreate,
    LoginAttemptCreate,
    MFASetupResponse,
    MFAVerificationRequest,
    MFAVerificationResponse,
    SecurityDashboard,
    RiskAssessment,
)
from app.core.security import hash_password
from app.config import get_settings

settings = get_settings()
logger = logging.getLogger(__name__)


class SecurityService:
    """Enhanced security service for RBAC, MFA, and audit logging."""

    def __init__(self, db: Session):
        self.db = db
        self.encryption_key = self._get_encryption_key()
        self.fernet = Fernet(self.encryption_key)

    def _get_encryption_key(self) -> bytes:
        """Get or generate encryption key for MFA secrets."""
        # In production, this should be stored securely (e.g., AWS KMS, HashiCorp Vault)
        key_data = settings.secret_key.encode()
        return base64.urlsafe_b64encode(hashlib.sha256(key_data).digest())

    def _encrypt_secret(self, secret: str) -> str:
        """Encrypt MFA secret."""
        try:
            encrypted_data = self.fernet.encrypt(secret.encode())
            return base64.b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error(f"Error encrypting secret: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to encrypt secret",
            )

    def _decrypt_secret(self, encrypted_secret: str) -> str:
        """Decrypt MFA secret."""
        try:
            encrypted_data = base64.b64decode(encrypted_secret.encode())
            decrypted_data = self.fernet.decrypt(encrypted_data)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"Error decrypting secret: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to decrypt secret",
            )

    # RBAC Methods
    async def create_role(self, role_data: RoleCreate, current_user: User) -> Role:
        """Create a new role with permissions."""
        try:
            # Check if role name already exists in organization
            existing_role = (
                self.db.query(Role)
                .filter(
                    Role.organization_id == current_user.organization_id,
                    Role.name == role_data.name,
                )
                .first()
            )

            if existing_role:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Role name already exists in organization",
                )

            # Create role
            db_role = Role(
                organization_id=current_user.organization_id,
                name=role_data.name,
                display_name=role_data.display_name,
                description=role_data.description,
                is_system_role=False,
                is_active=role_data.is_active,
                priority=role_data.priority,
            )

            self.db.add(db_role)
            self.db.flush()

            # Add permissions to role
            if role_data.permissions:
                for permission_id in role_data.permissions:
                    role_permission = RolePermission(
                        role_id=db_role.id,
                        permission_id=permission_id,
                        granted=True,
                        granted_by=current_user.id,
                    )
                    self.db.add(role_permission)

            self.db.commit()
            self.db.refresh(db_role)

            # Log security event
            await self.log_security_event(
                organization_id=current_user.organization_id,
                user_id=current_user.id,
                event_type="role_created",
                event_category="admin",
                event_description=f"Role '{role_data.name}' created",
                resource_type="role",
                resource_id=db_role.id,
                action="create",
            )

            logger.info(f"Role created: {db_role.id} by user {current_user.id}")
            return db_role

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error creating role: {e}")
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to create role",
            )

    async def assign_role_to_user(
        self, user_role_data: UserRoleCreate, current_user: User
    ) -> UserRole:
        """Assign a role to a user."""
        try:
            # Verify target user exists and is in same organization
            target_user = (
                self.db.query(User)
                .filter(
                    User.id == user_role_data.user_id,
                    User.organization_id == current_user.organization_id,
                )
                .first()
            )

            if not target_user:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="User not found"
                )

            # Verify role exists and is in same organization
            role = (
                self.db.query(Role)
                .filter(
                    Role.id == user_role_data.role_id,
                    Role.organization_id == current_user.organization_id,
                )
                .first()
            )

            if not role:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="Role not found"
                )

            # Check if user already has this role
            existing_assignment = (
                self.db.query(UserRole)
                .filter(
                    UserRole.user_id == user_role_data.user_id,
                    UserRole.role_id == user_role_data.role_id,
                    UserRole.is_active == True,
                )
                .first()
            )

            if existing_assignment:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="User already has this role",
                )

            # Create role assignment
            user_role = UserRole(
                user_id=user_role_data.user_id,
                role_id=user_role_data.role_id,
                valid_from=user_role_data.valid_from or datetime.utcnow(),
                valid_until=user_role_data.valid_until,
                conditions=user_role_data.conditions,
                assigned_by=current_user.id,
                reason=user_role_data.reason,
            )

            self.db.add(user_role)
            self.db.commit()
            self.db.refresh(user_role)

            # Log security event
            await self.log_security_event(
                organization_id=current_user.organization_id,
                user_id=current_user.id,
                event_type="role_assigned",
                event_category="authorization",
                event_description=f"Role '{role.name}' assigned to user {target_user.email}",
                resource_type="user_role",
                resource_id=user_role.id,
                action="create",
                additional_data={
                    "target_user_id": str(target_user.id),
                    "role_name": role.name,
                    "valid_until": user_role.valid_until.isoformat()
                    if user_role.valid_until
                    else None,
                },
            )

            logger.info(
                f"Role {role.name} assigned to user {target_user.id} by {current_user.id}"
            )
            return user_role

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error assigning role: {e}")
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to assign role",
            )

    def check_permission(
        self, user: User, permission_name: str, resource_id: Optional[str] = None
    ) -> bool:
        """Check if user has a specific permission."""
        try:
            # Get user's active roles
            user_roles = (
                self.db.query(UserRole)
                .filter(UserRole.user_id == user.id, UserRole.is_active == True)
                .all()
            )

            # Filter valid roles (time-based)
            valid_roles = [ur for ur in user_roles if ur.is_valid]

            if not valid_roles:
                return False

            # Get role IDs
            role_ids = [ur.role_id for ur in valid_roles]

            # Check if any role has the required permission
            role_permission = (
                self.db.query(RolePermission)
                .join(Permission)
                .filter(
                    RolePermission.role_id.in_(role_ids),
                    Permission.name == permission_name,
                    RolePermission.granted == True,
                    Permission.is_active == True,
                )
                .first()
            )

            return role_permission is not None

        except Exception as e:
            logger.error(f"Error checking permission: {e}")
            return False

    # MFA Methods
    async def setup_totp_mfa(self, user: User) -> MFASetupResponse:
        """Set up TOTP-based MFA for a user."""
        try:
            # Generate secret key
            secret = pyotp.random_base32()

            # Create TOTP object
            totp = pyotp.TOTP(secret)

            # Generate QR code
            provisioning_uri = totp.provisioning_uri(
                name=user.email, issuer_name=settings.app_name
            )

            # Create QR code image
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(provisioning_uri)
            qr.make(fit=True)

            img = qr.make_image(fill_color="black", back_color="white")
            img_buffer = io.BytesIO()
            img.save(img_buffer, format="PNG")
            img_buffer.seek(0)

            # Convert to base64 for frontend
            qr_code_data = base64.b64encode(img_buffer.getvalue()).decode()
            qr_code_url = f"data:image/png;base64,{qr_code_data}"

            # Generate backup codes
            backup_codes = [secrets.token_hex(4).upper() for _ in range(10)]
            encrypted_backup_codes = [
                self._encrypt_secret(code) for code in backup_codes
            ]

            # Create MFA method record
            mfa_method = MFAMethod(
                user_id=user.id,
                method_type="totp",
                secret_key=self._encrypt_secret(secret),
                backup_codes=encrypted_backup_codes,
                is_active=True,
                is_verified=False,
                recovery_codes_generated_at=datetime.utcnow(),
            )

            self.db.add(mfa_method)
            self.db.commit()
            self.db.refresh(mfa_method)

            # Log security event
            await self.log_security_event(
                organization_id=user.organization_id,
                user_id=user.id,
                event_type="mfa_setup_initiated",
                event_category="authentication",
                event_description="TOTP MFA setup initiated",
                resource_type="mfa_method",
                resource_id=mfa_method.id,
                action="create",
            )

            return MFASetupResponse(
                method_id=mfa_method.id,
                qr_code_url=qr_code_url,
                backup_codes=backup_codes,
                secret_key=secret,
            )

        except Exception as e:
            logger.error(f"Error setting up TOTP MFA: {e}")
            self.db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to setup MFA",
            )

    async def verify_mfa_code(
        self, verification_request: MFAVerificationRequest, user: User
    ) -> MFAVerificationResponse:
        """Verify MFA code."""
        try:
            # Get MFA method
            mfa_method = (
                self.db.query(MFAMethod)
                .filter(
                    MFAMethod.id == verification_request.method_id,
                    MFAMethod.user_id == user.id,
                    MFAMethod.is_active == True,
                )
                .first()
            )

            if not mfa_method:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND, detail="MFA method not found"
                )

            verified = False

            if mfa_method.method_type == "totp":
                # Decrypt secret and verify TOTP code
                secret = self._decrypt_secret(mfa_method.secret_key)
                totp = pyotp.TOTP(secret)

                # Verify code with window for clock skew
                verified = totp.verify(verification_request.code, valid_window=1)

                if not verified:
                    # Check if it's a backup code
                    if mfa_method.backup_codes:
                        for encrypted_code in mfa_method.backup_codes:
                            backup_code = self._decrypt_secret(encrypted_code)
                            if backup_code == verification_request.code.upper():
                                verified = True
                                # Remove used backup code
                                mfa_method.backup_codes.remove(encrypted_code)
                                mfa_method.recovery_codes_used += 1
                                break

            if verified:
                # Mark as verified if first time
                if not mfa_method.is_verified:
                    mfa_method.is_verified = True

                mfa_method.last_used_at = datetime.utcnow()
                mfa_method.usage_count += 1
                self.db.commit()

                # Log successful verification
                await self.log_security_event(
                    organization_id=user.organization_id,
                    user_id=user.id,
                    event_type="mfa_verification_success",
                    event_category="authentication",
                    event_description="MFA code verified successfully",
                    resource_type="mfa_method",
                    resource_id=mfa_method.id,
                    action="verify",
                )
            else:
                # Log failed verification
                await self.log_security_event(
                    organization_id=user.organization_id,
                    user_id=user.id,
                    event_type="mfa_verification_failed",
                    event_category="authentication",
                    event_description="MFA code verification failed",
                    resource_type="mfa_method",
                    resource_id=mfa_method.id,
                    action="verify",
                    additional_data={"code_provided": verification_request.code},
                )

            backup_codes_remaining = None
            if mfa_method.backup_codes:
                backup_codes_remaining = len(mfa_method.backup_codes)

            return MFAVerificationResponse(
                verified=verified, backup_codes_remaining=backup_codes_remaining
            )

        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error verifying MFA code: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to verify MFA code",
            )

    # Audit Logging
    async def log_security_event(
        self,
        organization_id: str,
        event_type: str,
        event_category: str,
        event_description: str,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        resource_type: Optional[str] = None,
        resource_id: Optional[str] = None,
        action: Optional[str] = None,
        ip_address: Optional[str] = None,
        user_agent: Optional[str] = None,
        request_id: Optional[str] = None,
        additional_data: Optional[Dict[str, Any]] = None,
        severity: str = "info",
        risk_score: int = 0,
    ) -> SecurityEvent:
        """Log a security event."""
        try:
            security_event = SecurityEvent(
                organization_id=organization_id,
                user_id=user_id,
                session_id=session_id,
                event_type=event_type,
                event_category=event_category,
                severity=severity,
                event_description=event_description,
                resource_type=resource_type,
                resource_id=resource_id,
                action=action,
                ip_address=ip_address,
                user_agent=user_agent,
                request_id=request_id,
                additional_data=additional_data or {},
                risk_score=risk_score,
            )

            self.db.add(security_event)
            self.db.commit()
            self.db.refresh(security_event)

            return security_event

        except Exception as e:
            logger.error(f"Error logging security event: {e}")
            # Don't raise exception for logging failures
            return None

    async def log_login_attempt(
        self, login_attempt_data: LoginAttemptCreate, session_id: Optional[str] = None
    ) -> LoginAttempt:
        """Log a login attempt."""
        try:
            # Calculate risk score based on various factors
            risk_score = self._calculate_login_risk_score(login_attempt_data)

            login_attempt = LoginAttempt(
                email=login_attempt_data.email,
                ip_address=login_attempt_data.ip_address,
                user_agent=login_attempt_data.user_agent,
                success=login_attempt_data.success,
                failure_reason=login_attempt_data.failure_reason,
                mfa_required=login_attempt_data.mfa_required,
                mfa_completed=login_attempt_data.mfa_completed,
                country=login_attempt_data.country,
                city=login_attempt_data.city,
                timezone=login_attempt_data.timezone,
                risk_score=risk_score,
                risk_factors=login_attempt_data.risk_factors or {},
                session_id=session_id,
            )

            self.db.add(login_attempt)
            self.db.commit()
            self.db.refresh(login_attempt)

            return login_attempt

        except Exception as e:
            logger.error(f"Error logging login attempt: {e}")
            return None

    def _calculate_login_risk_score(self, login_data: LoginAttemptCreate) -> int:
        """Calculate risk score for login attempt."""
        risk_score = 0

        # Failed login increases risk
        if not login_data.success:
            risk_score += 30

        # Check for recent failed attempts from same IP
        recent_failures = (
            self.db.query(LoginAttempt)
            .filter(
                LoginAttempt.ip_address == login_data.ip_address,
                LoginAttempt.success == False,
                LoginAttempt.attempted_at > datetime.utcnow() - timedelta(hours=1),
            )
            .count()
        )

        risk_score += min(recent_failures * 10, 50)

        # Geographic anomalies (simplified)
        if login_data.country and login_data.country not in ["US", "CA", "GB"]:
            risk_score += 20

        return min(risk_score, 100)

    # Security Dashboard and Analytics
    async def get_security_dashboard(self, organization_id: str) -> SecurityDashboard:
        """Get security dashboard summary."""
        try:
            # Get total users
            total_users = (
                self.db.query(User)
                .filter(User.organization_id == organization_id, User.is_active == True)
                .count()
            )

            # Get active sessions (last 24 hours)
            from app.models.user import UserSession

            active_sessions = (
                self.db.query(UserSession)
                .filter(
                    UserSession.organization_id == organization_id,
                    UserSession.is_active == True,
                    UserSession.last_activity_at
                    > datetime.utcnow() - timedelta(hours=24),
                )
                .count()
            )

            # Get failed logins in last 24 hours
            failed_logins_24h = (
                self.db.query(LoginAttempt)
                .filter(
                    LoginAttempt.success == False,
                    LoginAttempt.attempted_at > datetime.utcnow() - timedelta(hours=24),
                )
                .count()
            )

            # Get security events in last 24 hours
            security_events_24h = (
                self.db.query(SecurityEvent)
                .filter(
                    SecurityEvent.organization_id == organization_id,
                    SecurityEvent.timestamp > datetime.utcnow() - timedelta(hours=24),
                )
                .count()
            )

            # Get pending access requests
            pending_access_requests = (
                self.db.query(AccessRequest)
                .filter(
                    AccessRequest.organization_id == organization_id,
                    AccessRequest.status == "pending",
                )
                .count()
            )

            # Get high risk events
            high_risk_events = (
                self.db.query(SecurityEvent)
                .filter(
                    SecurityEvent.organization_id == organization_id,
                    SecurityEvent.risk_score >= 70,
                    SecurityEvent.timestamp > datetime.utcnow() - timedelta(hours=24),
                )
                .count()
            )

            # Get MFA adoption rate
            mfa_enabled_users = (
                self.db.query(User)
                .join(MFAMethod)
                .filter(
                    User.organization_id == organization_id,
                    User.is_active == True,
                    MFAMethod.is_active == True,
                    MFAMethod.is_verified == True,
                )
                .distinct()
                .count()
            )

            mfa_adoption_rate = (
                (mfa_enabled_users / total_users * 100) if total_users > 0 else 0
            )

            return SecurityDashboard(
                total_users=total_users,
                active_sessions=active_sessions,
                failed_logins_24h=failed_logins_24h,
                security_events_24h=security_events_24h,
                pending_access_requests=pending_access_requests,
                high_risk_events=high_risk_events,
                mfa_enabled_users=mfa_enabled_users,
                mfa_adoption_rate=round(mfa_adoption_rate, 2),
            )

        except Exception as e:
            logger.error(f"Error getting security dashboard: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to get security dashboard",
            )

    async def assess_organization_risk(self, organization_id: str) -> RiskAssessment:
        """Assess overall security risk for organization."""
        try:
            risk_factors = []
            recommendations = []
            overall_risk_score = 0

            # Check MFA adoption
            total_users = (
                self.db.query(User)
                .filter(User.organization_id == organization_id, User.is_active == True)
                .count()
            )

            mfa_users = (
                self.db.query(User)
                .join(MFAMethod)
                .filter(
                    User.organization_id == organization_id,
                    User.is_active == True,
                    MFAMethod.is_active == True,
                    MFAMethod.is_verified == True,
                )
                .distinct()
                .count()
            )

            mfa_adoption = (mfa_users / total_users * 100) if total_users > 0 else 0

            if mfa_adoption < 50:
                risk_factors.append(
                    {
                        "factor": "Low MFA Adoption",
                        "score": 30,
                        "description": f"Only {mfa_adoption:.1f}% of users have MFA enabled",
                    }
                )
                recommendations.append("Enforce MFA for all users")
                overall_risk_score += 30
            elif mfa_adoption < 80:
                risk_factors.append(
                    {
                        "factor": "Moderate MFA Adoption",
                        "score": 15,
                        "description": f"{mfa_adoption:.1f}% of users have MFA enabled",
                    }
                )
                recommendations.append("Increase MFA adoption to 100%")
                overall_risk_score += 15

            # Check for recent security incidents
            recent_incidents = (
                self.db.query(SecurityEvent)
                .filter(
                    SecurityEvent.organization_id == organization_id,
                    SecurityEvent.severity.in_(["error", "critical"]),
                    SecurityEvent.timestamp > datetime.utcnow() - timedelta(days=7),
                )
                .count()
            )

            if recent_incidents > 10:
                risk_factors.append(
                    {
                        "factor": "High Security Incident Rate",
                        "score": 25,
                        "description": f"{recent_incidents} security incidents in the last 7 days",
                    }
                )
                recommendations.append("Review and address security incidents")
                overall_risk_score += 25

            # Check for failed login patterns
            failed_logins = (
                self.db.query(LoginAttempt)
                .filter(
                    LoginAttempt.success == False,
                    LoginAttempt.attempted_at > datetime.utcnow() - timedelta(days=1),
                )
                .count()
            )

            if failed_logins > 50:
                risk_factors.append(
                    {
                        "factor": "High Failed Login Rate",
                        "score": 20,
                        "description": f"{failed_logins} failed login attempts in the last 24 hours",
                    }
                )
                recommendations.append("Implement account lockout policies")
                overall_risk_score += 20

            # Check password policy compliance
            # This would require additional password policy tracking

            # Cap overall risk score
            overall_risk_score = min(overall_risk_score, 100)

            return RiskAssessment(
                overall_risk_score=overall_risk_score,
                risk_factors=risk_factors,
                recommendations=recommendations,
                last_assessment=datetime.utcnow(),
            )

        except Exception as e:
            logger.error(f"Error assessing organization risk: {e}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to assess organization risk",
            )

    # Permission Management
    def get_user_permissions(self, user: User) -> List[str]:
        """Get all permissions for a user."""
        try:
            # Get user's active roles
            user_roles = (
                self.db.query(UserRole)
                .filter(UserRole.user_id == user.id, UserRole.is_active == True)
                .all()
            )

            # Filter valid roles (time-based)
            valid_roles = [ur for ur in user_roles if ur.is_valid]

            if not valid_roles:
                return []

            # Get role IDs
            role_ids = [ur.role_id for ur in valid_roles]

            # Get all permissions for these roles
            permissions = (
                self.db.query(Permission)
                .join(RolePermission)
                .filter(
                    RolePermission.role_id.in_(role_ids),
                    RolePermission.granted == True,
                    Permission.is_active == True,
                )
                .all()
            )

            return [p.name for p in permissions]

        except Exception as e:
            logger.error(f"Error getting user permissions: {e}")
            return []

    def initialize_default_permissions(self):
        """Initialize default system permissions."""
        try:
            default_permissions = [
                # Data permissions
                {
                    "name": "data.create",
                    "display_name": "Create Data",
                    "category": "data",
                    "action": "create",
                    "resource_type": "dataset",
                },
                {
                    "name": "data.read",
                    "display_name": "Read Data",
                    "category": "data",
                    "action": "read",
                    "resource_type": "dataset",
                },
                {
                    "name": "data.update",
                    "display_name": "Update Data",
                    "category": "data",
                    "action": "update",
                    "resource_type": "dataset",
                },
                {
                    "name": "data.delete",
                    "display_name": "Delete Data",
                    "category": "data",
                    "action": "delete",
                    "resource_type": "dataset",
                },
                # Analysis permissions
                {
                    "name": "analysis.create",
                    "display_name": "Create Analysis",
                    "category": "analysis",
                    "action": "create",
                    "resource_type": "analysis",
                },
                {
                    "name": "analysis.read",
                    "display_name": "Read Analysis",
                    "category": "analysis",
                    "action": "read",
                    "resource_type": "analysis",
                },
                {
                    "name": "analysis.update",
                    "display_name": "Update Analysis",
                    "category": "analysis",
                    "action": "update",
                    "resource_type": "analysis",
                },
                {
                    "name": "analysis.delete",
                    "display_name": "Delete Analysis",
                    "category": "analysis",
                    "action": "delete",
                    "resource_type": "analysis",
                },
                {
                    "name": "analysis.execute",
                    "display_name": "Execute Analysis",
                    "category": "analysis",
                    "action": "execute",
                    "resource_type": "analysis",
                },
                # Report permissions
                {
                    "name": "reports.create",
                    "display_name": "Create Reports",
                    "category": "reports",
                    "action": "create",
                    "resource_type": "report",
                },
                {
                    "name": "reports.read",
                    "display_name": "Read Reports",
                    "category": "reports",
                    "action": "read",
                    "resource_type": "report",
                },
                {
                    "name": "reports.update",
                    "display_name": "Update Reports",
                    "category": "reports",
                    "action": "update",
                    "resource_type": "report",
                },
                {
                    "name": "reports.delete",
                    "display_name": "Delete Reports",
                    "category": "reports",
                    "action": "delete",
                    "resource_type": "report",
                },
                {
                    "name": "reports.export",
                    "display_name": "Export Reports",
                    "category": "reports",
                    "action": "export",
                    "resource_type": "report",
                },
                # User management permissions
                {
                    "name": "users.create",
                    "display_name": "Create Users",
                    "category": "admin",
                    "action": "create",
                    "resource_type": "user",
                },
                {
                    "name": "users.read",
                    "display_name": "Read Users",
                    "category": "admin",
                    "action": "read",
                    "resource_type": "user",
                },
                {
                    "name": "users.update",
                    "display_name": "Update Users",
                    "category": "admin",
                    "action": "update",
                    "resource_type": "user",
                },
                {
                    "name": "users.delete",
                    "display_name": "Delete Users",
                    "category": "admin",
                    "action": "delete",
                    "resource_type": "user",
                },
                # Role management permissions
                {
                    "name": "roles.create",
                    "display_name": "Create Roles",
                    "category": "admin",
                    "action": "create",
                    "resource_type": "role",
                },
                {
                    "name": "roles.read",
                    "display_name": "Read Roles",
                    "category": "admin",
                    "action": "read",
                    "resource_type": "role",
                },
                {
                    "name": "roles.update",
                    "display_name": "Update Roles",
                    "category": "admin",
                    "action": "update",
                    "resource_type": "role",
                },
                {
                    "name": "roles.delete",
                    "display_name": "Delete Roles",
                    "category": "admin",
                    "action": "delete",
                    "resource_type": "role",
                },
                {
                    "name": "roles.assign",
                    "display_name": "Assign Roles",
                    "category": "admin",
                    "action": "assign",
                    "resource_type": "role",
                },
                # System permissions
                {
                    "name": "system.audit",
                    "display_name": "View Audit Logs",
                    "category": "admin",
                    "action": "read",
                    "resource_type": "audit",
                },
                {
                    "name": "system.security",
                    "display_name": "Manage Security",
                    "category": "admin",
                    "action": "manage",
                    "resource_type": "security",
                },
                {
                    "name": "system.settings",
                    "display_name": "Manage Settings",
                    "category": "admin",
                    "action": "manage",
                    "resource_type": "settings",
                },
            ]

            for perm_data in default_permissions:
                existing = (
                    self.db.query(Permission)
                    .filter(Permission.name == perm_data["name"])
                    .first()
                )

                if not existing:
                    permission = Permission(**perm_data)
                    self.db.add(permission)

            self.db.commit()
            logger.info("Default permissions initialized")

        except Exception as e:
            logger.error(f"Error initializing default permissions: {e}")
            self.db.rollback()

    def initialize_default_roles(self, organization_id: str):
        """Initialize default roles for an organization."""
        try:
            default_roles = [
                {
                    "name": "admin",
                    "display_name": "Administrator",
                    "description": "Full system access",
                    "priority": 100,
                    "permissions": ["*"],  # All permissions
                },
                {
                    "name": "manager",
                    "display_name": "Manager",
                    "description": "Management access with user oversight",
                    "priority": 75,
                    "permissions": [
                        "data.create",
                        "data.read",
                        "data.update",
                        "data.delete",
                        "analysis.create",
                        "analysis.read",
                        "analysis.update",
                        "analysis.delete",
                        "analysis.execute",
                        "reports.create",
                        "reports.read",
                        "reports.update",
                        "reports.delete",
                        "reports.export",
                        "users.read",
                        "users.update",
                        "roles.read",
                        "roles.assign",
                    ],
                },
                {
                    "name": "analyst",
                    "display_name": "Analyst",
                    "description": "Data analysis and reporting access",
                    "priority": 50,
                    "permissions": [
                        "data.create",
                        "data.read",
                        "data.update",
                        "analysis.create",
                        "analysis.read",
                        "analysis.update",
                        "analysis.execute",
                        "reports.create",
                        "reports.read",
                        "reports.update",
                        "reports.export",
                    ],
                },
                {
                    "name": "viewer",
                    "display_name": "Viewer",
                    "description": "Read-only access",
                    "priority": 25,
                    "permissions": ["data.read", "analysis.read", "reports.read"],
                },
            ]

            for role_data in default_roles:
                existing = (
                    self.db.query(Role)
                    .filter(
                        Role.organization_id == organization_id,
                        Role.name == role_data["name"],
                    )
                    .first()
                )

                if not existing:
                    role = Role(
                        organization_id=organization_id,
                        name=role_data["name"],
                        display_name=role_data["display_name"],
                        description=role_data["description"],
                        is_system_role=True,
                        priority=role_data["priority"],
                    )
                    self.db.add(role)
                    self.db.flush()

                    # Add permissions to role
                    if role_data["permissions"] == ["*"]:
                        # Add all permissions for admin role
                        all_permissions = (
                            self.db.query(Permission)
                            .filter(Permission.is_active == True)
                            .all()
                        )
                        for permission in all_permissions:
                            role_permission = RolePermission(
                                role_id=role.id,
                                permission_id=permission.id,
                                granted=True,
                            )
                            self.db.add(role_permission)
                    else:
                        # Add specific permissions
                        for perm_name in role_data["permissions"]:
                            permission = (
                                self.db.query(Permission)
                                .filter(Permission.name == perm_name)
                                .first()
                            )
                            if permission:
                                role_permission = RolePermission(
                                    role_id=role.id,
                                    permission_id=permission.id,
                                    granted=True,
                                )
                                self.db.add(role_permission)

            self.db.commit()
            logger.info(f"Default roles initialized for organization {organization_id}")

        except Exception as e:
            logger.error(f"Error initializing default roles: {e}")
            self.db.rollback()
