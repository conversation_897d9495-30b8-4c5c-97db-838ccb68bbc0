"""
JuliusAI Data Connector Models
"""
import uuid
from sqlalchemy import Column, String, Integer, Float, Boolean, DateTime, Text, ForeignKey, JSON as JSONB
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from datetime import datetime
from typing import Dict, Any, Optional, List

from app.database import Base


class DataSource(Base):
    """External data source configuration."""
    
    __tablename__ = "data_sources"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    source_type = Column(String(100), nullable=False)  # api, database, cloud_storage, file_share
    provider = Column(String(100), nullable=False)  # yahoo_finance, alpha_vantage, aws_s3, etc.
    
    # Connection Configuration
    connection_config = Column(JSONB, nullable=False)  # Provider-specific configuration
    authentication_config = Column(JSONB)  # Encrypted authentication details
    sync_config = Column(JSONB)  # Synchronization settings
    
    # Data Mapping
    data_mapping = Column(JSONB)  # Field mapping configuration
    transformation_rules = Column(JSONB)  # Data transformation rules
    
    # Status and Monitoring
    status = Column(String(50), default="inactive", nullable=False)  # inactive, active, error, testing
    last_sync_at = Column(DateTime(timezone=True))
    last_sync_status = Column(String(50))  # success, failed, partial
    last_sync_records = Column(Integer, default=0)
    last_error_message = Column(Text)
    
    # Scheduling
    sync_frequency = Column(String(50))  # manual, hourly, daily, weekly, monthly
    sync_schedule = Column(String(100))  # Cron expression for custom schedules
    auto_sync_enabled = Column(Boolean, default=False)
    next_sync_at = Column(DateTime(timezone=True))
    
    # Metadata
    tags = Column(JSONB)  # Array of tags
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    organization = relationship("Organization")
    user = relationship("User")
    sync_jobs = relationship("DataSyncJob", back_populates="data_source", cascade="all, delete-orphan")
    datasets = relationship("Dataset", back_populates="data_source")
    
    def __repr__(self):
        return f"<DataSource(id={self.id}, name='{self.name}', provider='{self.provider}')>"


class DataSyncJob(Base):
    """Data synchronization job tracking."""
    
    __tablename__ = "data_sync_jobs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    data_source_id = Column(UUID(as_uuid=True), ForeignKey("data_sources.id"), nullable=False)
    job_type = Column(String(50), nullable=False)  # full_sync, incremental, test, manual
    status = Column(String(50), default="pending", nullable=False)  # pending, running, completed, failed, cancelled
    
    # Job Configuration
    sync_parameters = Column(JSONB)  # Job-specific parameters
    date_range_start = Column(DateTime(timezone=True))  # For incremental syncs
    date_range_end = Column(DateTime(timezone=True))
    
    # Progress Tracking
    progress_percentage = Column(Integer, default=0)
    records_processed = Column(Integer, default=0)
    records_created = Column(Integer, default=0)
    records_updated = Column(Integer, default=0)
    records_failed = Column(Integer, default=0)
    
    # Execution Details
    started_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    error_message = Column(Text)
    error_details = Column(JSONB)  # Detailed error information
    
    # Results
    result_summary = Column(JSONB)  # Summary of sync results
    data_quality_report = Column(JSONB)  # Data quality assessment
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    data_source = relationship("DataSource", back_populates="sync_jobs")
    
    @property
    def duration_seconds(self) -> Optional[int]:
        """Calculate job duration in seconds."""
        if self.started_at and self.completed_at:
            return int((self.completed_at - self.started_at).total_seconds())
        return None
    
    @property
    def is_running(self) -> bool:
        """Check if job is currently running."""
        return self.status == "running"
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate of processed records."""
        if self.records_processed > 0:
            successful_records = self.records_created + self.records_updated
            return (successful_records / self.records_processed) * 100
        return 0.0
    
    def __repr__(self):
        return f"<DataSyncJob(id={self.id}, type='{self.job_type}', status='{self.status}')>"


class APICredential(Base):
    """Encrypted API credentials for external data sources."""
    
    __tablename__ = "api_credentials"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    organization_id = Column(UUID(as_uuid=True), ForeignKey("organizations.id"), nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    provider = Column(String(100), nullable=False)  # yahoo_finance, alpha_vantage, etc.
    
    # Encrypted Credentials
    encrypted_credentials = Column(Text, nullable=False)  # JSON credentials encrypted with organization key
    credential_type = Column(String(50), nullable=False)  # api_key, oauth, basic_auth, token
    
    # Metadata
    expires_at = Column(DateTime(timezone=True))  # For tokens that expire
    last_used_at = Column(DateTime(timezone=True))
    usage_count = Column(Integer, default=0)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False)  # Whether credentials have been tested
    verification_date = Column(DateTime(timezone=True))
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    organization = relationship("Organization")
    user = relationship("User")
    
    def __repr__(self):
        return f"<APICredential(id={self.id}, provider='{self.provider}', type='{self.credential_type}')>"


class DataConnectorTemplate(Base):
    """Pre-configured templates for common data sources."""
    
    __tablename__ = "data_connector_templates"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    provider = Column(String(100), nullable=False)
    category = Column(String(100))  # financial, accounting, cloud_storage, database
    
    # Template Configuration
    connection_template = Column(JSONB, nullable=False)  # Template for connection config
    authentication_template = Column(JSONB, nullable=False)  # Template for auth config
    data_mapping_template = Column(JSONB)  # Default data mapping
    sync_config_template = Column(JSONB)  # Default sync configuration
    
    # Template Metadata
    supported_operations = Column(JSONB)  # List of supported operations
    required_credentials = Column(JSONB)  # Required credential fields
    optional_parameters = Column(JSONB)  # Optional configuration parameters
    data_schema = Column(JSONB)  # Expected data schema
    
    # Usage and Ratings
    usage_count = Column(Integer, default=0)
    average_rating = Column(Float, default=0.0)
    
    # Status
    is_system_template = Column(Boolean, default=True)  # Built-in vs custom template
    is_active = Column(Boolean, default=True, nullable=False)
    version = Column(String(20), default="1.0")
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    def __repr__(self):
        return f"<DataConnectorTemplate(id={self.id}, name='{self.name}', provider='{self.provider}')>"


class DataTransformation(Base):
    """Data transformation rules and configurations."""
    
    __tablename__ = "data_transformations"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    data_source_id = Column(UUID(as_uuid=True), ForeignKey("data_sources.id"), nullable=False)
    name = Column(String(255), nullable=False)
    description = Column(Text)
    transformation_type = Column(String(50), nullable=False)  # mapping, filter, aggregate, calculate, clean
    
    # Transformation Configuration
    source_fields = Column(JSONB, nullable=False)  # Source field specifications
    target_fields = Column(JSONB, nullable=False)  # Target field specifications
    transformation_rules = Column(JSONB, nullable=False)  # Transformation logic
    validation_rules = Column(JSONB)  # Data validation rules
    
    # Execution Settings
    execution_order = Column(Integer, default=1)  # Order of execution
    is_enabled = Column(Boolean, default=True)
    error_handling = Column(String(50), default="skip")  # skip, fail, default_value
    
    # Performance Metrics
    last_execution_time_ms = Column(Integer)
    average_execution_time_ms = Column(Integer)
    success_rate = Column(Float, default=100.0)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relationships
    data_source = relationship("DataSource")
    
    def __repr__(self):
        return f"<DataTransformation(id={self.id}, name='{self.name}', type='{self.transformation_type}')>"


class DataSourceMetric(Base):
    """Performance and usage metrics for data sources."""
    
    __tablename__ = "data_source_metrics"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    data_source_id = Column(UUID(as_uuid=True), ForeignKey("data_sources.id"), nullable=False)
    metric_date = Column(DateTime(timezone=True), nullable=False)
    
    # Sync Metrics
    sync_count = Column(Integer, default=0)
    successful_syncs = Column(Integer, default=0)
    failed_syncs = Column(Integer, default=0)
    total_records_synced = Column(Integer, default=0)
    average_sync_duration_seconds = Column(Integer, default=0)
    
    # Data Quality Metrics
    data_quality_score = Column(Float, default=0.0)
    duplicate_records = Column(Integer, default=0)
    invalid_records = Column(Integer, default=0)
    missing_values_percentage = Column(Float, default=0.0)
    
    # Performance Metrics
    api_response_time_ms = Column(Integer, default=0)
    throughput_records_per_second = Column(Float, default=0.0)
    error_rate_percentage = Column(Float, default=0.0)
    
    # Usage Metrics
    active_users = Column(Integer, default=0)
    queries_executed = Column(Integer, default=0)
    data_volume_mb = Column(Float, default=0.0)
    
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    data_source = relationship("DataSource")
    
    def __repr__(self):
        return f"<DataSourceMetric(id={self.id}, date='{self.metric_date}', quality_score={self.data_quality_score})>"
