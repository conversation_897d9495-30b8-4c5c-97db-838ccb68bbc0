# JuliusAI Backend Dependencies
# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
asyncpg==0.29.0

# Authentication & Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6
bcrypt==4.1.2

# Data Processing
pandas==2.1.4
numpy==1.25.2
openpyxl==3.1.2
xlrd==2.0.1

# AI/ML Libraries
scikit-learn==1.3.2
tensorflow==2.14.0
prophet==1.1.5
statsmodels==0.14.0

# Report Generation & NLP
jinja2==3.1.2
reportlab==4.0.7
weasyprint==60.2
python-docx==1.1.0
python-pptx==0.6.23
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0
openai==1.3.7
transformers==4.36.2

# Task Queue
celery==5.3.4
redis==5.0.1

# Validation & Serialization
pydantic==2.5.0
pydantic-settings==2.1.0

# HTTP Client
httpx==0.25.2
aiofiles==23.2.1

# External Data Sources & APIs
yfinance==0.2.28
alpha-vantage==2.3.1
quandl==3.7.0
requests==2.31.0
requests-oauthlib==1.3.1
boto3==1.34.0
google-cloud-storage==2.10.0
dropbox==11.36.2
pymongo==4.6.0
psycopg2-binary==2.9.9
mysql-connector-python==8.2.0
sqlalchemy-utils==0.41.1

# Development & Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Monitoring & Logging
structlog==23.2.0
sentry-sdk[fastapi]==1.38.0

# Environment
python-dotenv==1.0.0
