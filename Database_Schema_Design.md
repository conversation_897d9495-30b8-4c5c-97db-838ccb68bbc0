# JuliusAI Database Schema and Data Model

**Version:** 2.0
**Date:** 2025-01-27
**Phase:** Phase 2 - System Design & Architecture
**Database:** PostgreSQL 15+ with TimescaleDB extension

## Table of Contents
1. [Database Architecture](#database-architecture)
2. [Core Schema Design](#core-schema-design)
3. [Indexing Strategy](#indexing-strategy)
4. [Data Relationships](#data-relationships)
5. [Security & Compliance](#security--compliance)
6. [Performance Optimization](#performance-optimization)
7. [Migration Strategy](#migration-strategy)

---

## 1. Database Architecture

### 1.1 Database Structure
```
JuliusAI Database Ecosystem
├── Primary Database (PostgreSQL)
│   ├── User Management Schema
│   ├── Data Management Schema
│   ├── Analysis Schema
│   ├── Reporting Schema
│   └── Audit Schema
├── Time-Series Database (TimescaleDB)
│   ├── Financial Metrics
│   ├── Performance Data
│   └── Real-time Analytics
├── Cache Layer (Redis)
│   ├── Session Storage
│   ├── API Response Cache
│   └── Real-time Data
└── Search Engine (Elasticsearch)
    ├── Full-text Search
    ├── Log Analysis
    └── Data Discovery
```

### 1.2 Schema Organization
- **Logical Separation:** Each domain has its own schema
- **Multi-tenancy:** Organization-level data isolation
- **Audit Trail:** Complete activity tracking
- **Data Versioning:** Historical data preservation

---

## 2. Core Schema Design

### 2.1 User Management Schema

```sql
-- Organizations table
CREATE TABLE organizations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    subscription_tier VARCHAR(50) DEFAULT 'basic',
    max_users INTEGER DEFAULT 10,
    max_datasets INTEGER DEFAULT 100,
    storage_limit_gb INTEGER DEFAULT 10,
    settings JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    
    CONSTRAINT valid_subscription_tier CHECK (subscription_tier IN ('basic', 'professional', 'enterprise')),
    CONSTRAINT positive_limits CHECK (max_users > 0 AND max_datasets > 0 AND storage_limit_gb > 0)
);

-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100) NOT NULL,
    last_name VARCHAR(100) NOT NULL,
    role VARCHAR(50) DEFAULT 'analyst',
    avatar_url VARCHAR(500),
    phone VARCHAR(20),
    timezone VARCHAR(50) DEFAULT 'UTC',
    language VARCHAR(10) DEFAULT 'en',
    preferences JSONB DEFAULT '{}',
    last_login_at TIMESTAMP WITH TIME ZONE,
    email_verified_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true,
    
    CONSTRAINT valid_role CHECK (role IN ('admin', 'manager', 'analyst', 'viewer')),
    CONSTRAINT valid_email CHECK (email ~* '^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$')
);

-- User sessions table
CREATE TABLE user_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    token_hash VARCHAR(255) NOT NULL,
    refresh_token_hash VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT true
);

-- User permissions table
CREATE TABLE user_permissions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    resource_type VARCHAR(50) NOT NULL,
    resource_id UUID,
    permission VARCHAR(50) NOT NULL,
    granted_by UUID REFERENCES users(id),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP WITH TIME ZONE,
    
    CONSTRAINT valid_permission CHECK (permission IN ('read', 'write', 'delete', 'admin', 'share')),
    UNIQUE(user_id, resource_type, resource_id, permission)
);
```

### 2.2 Data Management Schema

```sql
-- Datasets table
CREATE TABLE datasets (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE SET NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    file_name VARCHAR(255),
    file_path VARCHAR(500),
    file_size BIGINT,
    file_type VARCHAR(50),
    mime_type VARCHAR(100),
    checksum VARCHAR(64),
    row_count INTEGER,
    column_count INTEGER,
    status VARCHAR(50) DEFAULT 'uploaded',
    processing_started_at TIMESTAMP WITH TIME ZONE,
    processing_completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    metadata JSONB DEFAULT '{}',
    data_quality_score DECIMAL(3,2),
    tags TEXT[],
    is_public BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_status CHECK (status IN ('uploaded', 'processing', 'processed', 'failed', 'archived')),
    CONSTRAINT positive_counts CHECK (row_count >= 0 AND column_count >= 0),
    CONSTRAINT valid_quality_score CHECK (data_quality_score >= 0 AND data_quality_score <= 1)
);

-- Data columns table
CREATE TABLE data_columns (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    dataset_id UUID NOT NULL REFERENCES datasets(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255),
    position INTEGER NOT NULL,
    data_type VARCHAR(50) NOT NULL,
    is_numeric BOOLEAN DEFAULT false,
    is_date BOOLEAN DEFAULT false,
    is_categorical BOOLEAN DEFAULT false,
    null_count INTEGER DEFAULT 0,
    unique_count INTEGER,
    min_value DECIMAL,
    max_value DECIMAL,
    mean_value DECIMAL,
    median_value DECIMAL,
    std_deviation DECIMAL,
    sample_values JSONB,
    format_pattern VARCHAR(100),
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_data_type CHECK (data_type IN ('integer', 'decimal', 'text', 'date', 'datetime', 'boolean', 'json')),
    CONSTRAINT positive_position CHECK (position > 0),
    UNIQUE(dataset_id, name),
    UNIQUE(dataset_id, position)
);

-- Data validation rules table
CREATE TABLE data_validation_rules (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    dataset_id UUID NOT NULL REFERENCES datasets(id) ON DELETE CASCADE,
    column_id UUID REFERENCES data_columns(id) ON DELETE CASCADE,
    rule_type VARCHAR(50) NOT NULL,
    rule_config JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_rule_type CHECK (rule_type IN ('required', 'range', 'format', 'unique', 'custom'))
);

-- Data processing jobs table
CREATE TABLE data_processing_jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    dataset_id UUID NOT NULL REFERENCES datasets(id) ON DELETE CASCADE,
    job_type VARCHAR(50) NOT NULL,
    status VARCHAR(50) DEFAULT 'pending',
    progress_percentage INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    result_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_job_type CHECK (job_type IN ('parse', 'validate', 'clean', 'transform', 'analyze')),
    CONSTRAINT valid_status CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    CONSTRAINT valid_progress CHECK (progress_percentage >= 0 AND progress_percentage <= 100)
);
```

### 2.3 Analysis Schema

```sql
-- Analyses table
CREATE TABLE analyses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    dataset_id UUID NOT NULL REFERENCES datasets(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE SET NULL,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    analysis_type VARCHAR(100) NOT NULL,
    parameters JSONB DEFAULT '{}',
    status VARCHAR(50) DEFAULT 'pending',
    progress_percentage INTEGER DEFAULT 0,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    results JSONB,
    insights JSONB,
    confidence_score DECIMAL(3,2),
    execution_time_ms INTEGER,
    tags TEXT[],
    is_favorite BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_analysis_type CHECK (analysis_type IN ('descriptive', 'trend', 'forecast', 'anomaly', 'correlation', 'custom')),
    CONSTRAINT valid_status CHECK (status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    CONSTRAINT valid_confidence CHECK (confidence_score >= 0 AND confidence_score <= 1)
);

-- Analysis metrics table
CREATE TABLE analysis_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    analysis_id UUID NOT NULL REFERENCES analyses(id) ON DELETE CASCADE,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL,
    metric_unit VARCHAR(50),
    metric_category VARCHAR(50),
    calculation_method VARCHAR(100),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(analysis_id, metric_name)
);

-- Forecasts table
CREATE TABLE forecasts (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    analysis_id UUID NOT NULL REFERENCES analyses(id) ON DELETE CASCADE,
    model_type VARCHAR(50) NOT NULL,
    forecast_horizon INTEGER NOT NULL,
    forecast_data JSONB NOT NULL,
    confidence_intervals JSONB,
    model_accuracy DECIMAL(5,4),
    model_parameters JSONB,
    validation_metrics JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_model_type CHECK (model_type IN ('linear', 'arima', 'prophet', 'lstm', 'ensemble')),
    CONSTRAINT positive_horizon CHECK (forecast_horizon > 0)
);

-- Anomalies table
CREATE TABLE anomalies (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    analysis_id UUID NOT NULL REFERENCES analyses(id) ON DELETE CASCADE,
    data_point_date DATE NOT NULL,
    metric_name VARCHAR(100) NOT NULL,
    actual_value DECIMAL NOT NULL,
    expected_value DECIMAL,
    anomaly_score DECIMAL(5,4) NOT NULL,
    severity VARCHAR(20) DEFAULT 'medium',
    description TEXT,
    is_acknowledged BOOLEAN DEFAULT false,
    acknowledged_by UUID REFERENCES users(id),
    acknowledged_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_severity CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    CONSTRAINT valid_anomaly_score CHECK (anomaly_score >= 0 AND anomaly_score <= 1)
);
```

### 2.4 Reporting Schema

```sql
-- Report templates table
CREATE TABLE report_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    template_type VARCHAR(50) NOT NULL,
    template_config JSONB NOT NULL,
    is_public BOOLEAN DEFAULT false,
    is_system_template BOOLEAN DEFAULT false,
    created_by UUID REFERENCES users(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_template_type CHECK (template_type IN ('dashboard', 'executive', 'detailed', 'custom'))
);

-- Reports table
CREATE TABLE reports (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
    analysis_id UUID REFERENCES analyses(id) ON DELETE SET NULL,
    template_id UUID REFERENCES report_templates(id) ON DELETE SET NULL,
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE SET NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    content JSONB NOT NULL,
    format VARCHAR(20) DEFAULT 'json',
    file_path VARCHAR(500),
    file_size BIGINT,
    status VARCHAR(50) DEFAULT 'draft',
    is_shared BOOLEAN DEFAULT false,
    share_token VARCHAR(100) UNIQUE,
    share_expires_at TIMESTAMP WITH TIME ZONE,
    generated_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_format CHECK (format IN ('json', 'pdf', 'excel', 'html')),
    CONSTRAINT valid_status CHECK (status IN ('draft', 'generating', 'completed', 'failed', 'archived'))
);

-- Report shares table
CREATE TABLE report_shares (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    report_id UUID NOT NULL REFERENCES reports(id) ON DELETE CASCADE,
    shared_by UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    shared_with_email VARCHAR(255),
    shared_with_user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    permission_level VARCHAR(20) DEFAULT 'view',
    expires_at TIMESTAMP WITH TIME ZONE,
    accessed_at TIMESTAMP WITH TIME ZONE,
    access_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_permission CHECK (permission_level IN ('view', 'comment', 'edit')),
    CONSTRAINT share_target_check CHECK (
        (shared_with_email IS NOT NULL AND shared_with_user_id IS NULL) OR
        (shared_with_email IS NULL AND shared_with_user_id IS NOT NULL)
    )
);
```

### 2.5 Audit Schema

```sql
-- Audit logs table
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    organization_id UUID REFERENCES organizations(id) ON DELETE SET NULL,
    user_id UUID REFERENCES users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id UUID,
    request_id VARCHAR(100),
    severity VARCHAR(20) DEFAULT 'info',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_severity CHECK (severity IN ('debug', 'info', 'warning', 'error', 'critical'))
);

-- System events table
CREATE TABLE system_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    event_type VARCHAR(100) NOT NULL,
    event_data JSONB NOT NULL,
    severity VARCHAR(20) DEFAULT 'info',
    source_service VARCHAR(50),
    correlation_id VARCHAR(100),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    CONSTRAINT valid_event_severity CHECK (severity IN ('debug', 'info', 'warning', 'error', 'critical'))
);
```

---

## 3. Indexing Strategy

### 3.1 Primary Indexes
```sql
-- Performance-critical indexes
CREATE INDEX CONCURRENTLY idx_datasets_org_user ON datasets(organization_id, user_id);
CREATE INDEX CONCURRENTLY idx_datasets_status ON datasets(status) WHERE status != 'archived';
CREATE INDEX CONCURRENTLY idx_analyses_dataset ON analyses(dataset_id);
CREATE INDEX CONCURRENTLY idx_analyses_user_created ON analyses(user_id, created_at DESC);
CREATE INDEX CONCURRENTLY idx_reports_org_created ON reports(organization_id, created_at DESC);
CREATE INDEX CONCURRENTLY idx_audit_logs_org_created ON audit_logs(organization_id, created_at DESC);

-- Search and filtering indexes
CREATE INDEX CONCURRENTLY idx_datasets_tags ON datasets USING GIN(tags);
CREATE INDEX CONCURRENTLY idx_datasets_metadata ON datasets USING GIN(metadata);
CREATE INDEX CONCURRENTLY idx_analyses_results ON analyses USING GIN(results);

-- Time-based indexes for analytics
CREATE INDEX CONCURRENTLY idx_analyses_completed_at ON analyses(completed_at) WHERE completed_at IS NOT NULL;
CREATE INDEX CONCURRENTLY idx_anomalies_date_severity ON anomalies(data_point_date, severity);
```

### 3.2 Composite Indexes
```sql
-- Multi-column indexes for complex queries
CREATE INDEX CONCURRENTLY idx_user_sessions_active ON user_sessions(user_id, is_active, expires_at);
CREATE INDEX CONCURRENTLY idx_data_columns_dataset_type ON data_columns(dataset_id, data_type);
CREATE INDEX CONCURRENTLY idx_analysis_metrics_analysis_category ON analysis_metrics(analysis_id, metric_category);
```

---

## 4. Data Relationships

### 4.1 Entity Relationship Diagram
```
Organizations (1) ──── (N) Users
     │                    │
     │                    │
     └── (N) Datasets ──── (N) Analyses
            │                 │
            │                 ├── (N) Metrics
            │                 ├── (N) Forecasts
            │                 └── (N) Anomalies
            │
            └── (N) Data Columns
                    │
                    └── (N) Validation Rules

Users (1) ──── (N) Reports ──── (N) Report Shares
  │                │
  │                └── (1) Report Templates
  │
  └── (N) User Sessions
      │
      └── (N) User Permissions
```

### 4.2 Data Integrity Constraints
- **Referential Integrity:** Foreign key constraints with appropriate cascade rules
- **Data Validation:** Check constraints for enum values and ranges
- **Unique Constraints:** Prevent duplicate data where appropriate
- **Not Null Constraints:** Ensure required fields are always populated

---

## 5. Security & Compliance

### 5.1 Data Encryption
```sql
-- Enable row-level security
ALTER TABLE datasets ENABLE ROW LEVEL SECURITY;
ALTER TABLE analyses ENABLE ROW LEVEL SECURITY;
ALTER TABLE reports ENABLE ROW LEVEL SECURITY;

-- Organization-level data isolation
CREATE POLICY org_isolation_datasets ON datasets
    FOR ALL TO application_role
    USING (organization_id = current_setting('app.current_org_id')::UUID);

CREATE POLICY org_isolation_analyses ON analyses
    FOR ALL TO application_role
    USING (organization_id = current_setting('app.current_org_id')::UUID);
```

### 5.2 Audit Trail
- **Complete Activity Logging:** All data modifications tracked
- **User Action Tracking:** Detailed user behavior analysis
- **System Event Monitoring:** Infrastructure and application events
- **Data Lineage:** Track data transformations and dependencies

---

## 6. Performance Optimization

### 6.1 Partitioning Strategy
```sql
-- Partition audit logs by month
CREATE TABLE audit_logs_y2025m01 PARTITION OF audit_logs
    FOR VALUES FROM ('2025-01-01') TO ('2025-02-01');

-- Partition time-series data by week
CREATE TABLE financial_metrics_w202501 PARTITION OF financial_metrics
    FOR VALUES FROM ('2025-01-01') TO ('2025-01-08');
```

### 6.2 Query Optimization
- **Connection Pooling:** PgBouncer for connection management
- **Read Replicas:** Separate read and write workloads
- **Materialized Views:** Pre-computed aggregations for dashboards
- **Query Plan Analysis:** Regular EXPLAIN ANALYZE for optimization

---

## 7. Migration Strategy

### 7.1 Database Versioning
```sql
-- Migration tracking table
CREATE TABLE schema_migrations (
    version VARCHAR(50) PRIMARY KEY,
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);
```

### 7.2 Deployment Process
1. **Backup Creation:** Full database backup before migration
2. **Schema Validation:** Dry-run migration in staging environment
3. **Incremental Migration:** Apply changes in small, reversible steps
4. **Data Validation:** Verify data integrity after migration
5. **Rollback Plan:** Prepared rollback scripts for each migration

---

**Next Steps:**
1. Create database migration scripts
2. Set up development database environment
3. Implement data access layer (ORM models)
4. Create database seeding scripts for testing
5. Establish backup and monitoring procedures
